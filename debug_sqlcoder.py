#!/usr/bin/env python3
"""
Debug sqlcoder model to see why it's generating empty queries
"""

import requests
from config import OLLAMA_BASE_URL, MODELS

def test_sqlcoder_directly():
    """Test sqlcoder model directly"""
    
    schema_info = """Database Tables:

categories:
  - id (INTEGER)
  - title (TEXT)
  - description (TEXT)

inventory:
  - id (INTEGER)
  - title (TEXT)
  - description (TEXT)
  - quantity (INTEGER)
  - category_id (INTEGER)"""
    
    test_prompts = [
        # Simple prompt
        "SELECT * FROM categories;",
        
        # Detailed prompt
        f"""Generate a SQL query to show all categories.

Database Schema:
{schema_info}

Query: Show me all categories""",
        
        # Very specific prompt
        f"""You are a SQL expert. Write a SELECT query to get all records from the categories table.

Schema:
{schema_info}

Request: Show all categories

SQL:""",
        
        # Different format
        f"""### Task
Write SQL to list all categories

### Schema
{schema_info}

### SQL Query:"""
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n🧪 Test {i}: Testing sqlcoder with different prompt")
        print(f"📝 Prompt: {prompt[:100]}...")
        print("-" * 50)
        
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": MODELS["sql_specialist"],  # sqlcoder
                    "messages": [{"role": "user", "content": prompt}],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["message"]["content"].strip()
                
                print(f"✅ Response received!")
                print(f"💬 Content: '{content}'")
                print(f"📏 Length: {len(content)} characters")
                
                if content and content != ";":
                    print("🎉 Non-empty response!")
                else:
                    print("❌ Empty or just semicolon")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_alternative_models():
    """Test other models for SQL generation"""
    
    schema_info = """Database Tables:
categories: id, title, description
inventory: id, title, description, quantity, category_id"""
    
    models_to_test = [
        ("qwen2.5:7b", "coordinator"),
        ("mistral", "tool_specialist")
    ]
    
    prompt = f"""Generate a SQL query to show all categories.

Database Schema:
{schema_info}

Request: Show me all categories

Respond with only the SQL query."""
    
    for model_name, model_key in models_to_test:
        print(f"\n🧪 Testing {model_name} for SQL generation")
        print("-" * 40)
        
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": model_name,
                    "messages": [{"role": "user", "content": prompt}],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["message"]["content"].strip()
                
                print(f"✅ Response: '{content}'")
                
                # Extract SQL if it's in markdown
                if "```sql" in content:
                    sql_start = content.find("```sql") + 6
                    sql_end = content.find("```", sql_start)
                    sql_query = content[sql_start:sql_end].strip()
                    print(f"🗃️ Extracted SQL: '{sql_query}'")
                elif "SELECT" in content.upper():
                    print("🗃️ Contains SELECT statement")
                else:
                    print("❌ No SQL detected")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🔍 Debugging SQL Generation")
    print("=" * 50)
    
    # Test sqlcoder directly
    test_sqlcoder_directly()
    
    # Test alternative models
    print("\n" + "=" * 50)
    print("🔄 Testing Alternative Models")
    test_alternative_models()
