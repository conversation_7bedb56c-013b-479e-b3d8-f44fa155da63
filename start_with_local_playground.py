#!/usr/bin/env python3
"""
Startup script that launches both Shago AI Assistant and the local playground.
This provides a complete offline experience.
"""

import subprocess
import time
import sys
import signal
import os
from pathlib import Path

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully."""
    print('\n👋 Shutting down Shago and local playground...')
    sys.exit(0)

def main():
    """Start both Shago and the local playground."""
    print("🚀 Starting Shago AI Assistant with Local Playground")
    print("=" * 60)
    
    # Register signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Start Shago AI Assistant in the background
        print("🤖 Starting Shago AI Assistant...")
        shago_process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a bit for Shago to start
        print("⏳ Waiting for Shago to initialize...")
        time.sleep(5)
        
        # Check if Shago started successfully
        if shago_process.poll() is not None:
            print("❌ Failed to start Shago AI Assistant")
            return 1
        
        print("✅ Shago AI Assistant started successfully")
        
        # Start local playground
        print("🌐 Starting Local Playground...")
        playground_process = subprocess.Popen(
            [sys.executable, "serve_local_playground.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        time.sleep(2)
        
        # Check if playground started successfully
        if playground_process.poll() is not None:
            print("❌ Failed to start Local Playground")
            shago_process.terminate()
            return 1
        
        print("✅ Local Playground started successfully")
        print("=" * 60)
        print("🎉 Both services are now running!")
        print("")
        print("📍 Access Points:")
        print("   🤖 Shago API: http://localhost:3023")
        print("   🌐 Local Playground: http://localhost:8080/local_playground.html")
        print("   📚 API Docs: http://localhost:3023/docs")
        print("")
        print("🔧 Features Available:")
        print("   • Complete offline operation")
        print("   • Multi-agent AI system")
        print("   • Database queries")
        print("   • Tool operations")
        print("   • Persistent memory")
        print("")
        print("🛑 Press Ctrl+C to stop both services")
        print("=" * 60)
        
        # Keep both processes running
        try:
            while True:
                # Check if either process has died
                if shago_process.poll() is not None:
                    print("❌ Shago AI Assistant stopped unexpectedly")
                    playground_process.terminate()
                    break
                
                if playground_process.poll() is not None:
                    print("❌ Local Playground stopped unexpectedly")
                    shago_process.terminate()
                    break
                
                time.sleep(1)
        
        except KeyboardInterrupt:
            print("\n👋 Shutting down services...")
        
        finally:
            # Clean up processes
            print("🧹 Cleaning up...")
            try:
                shago_process.terminate()
                playground_process.terminate()
                
                # Wait for processes to terminate
                shago_process.wait(timeout=5)
                playground_process.wait(timeout=5)
                
            except subprocess.TimeoutExpired:
                print("⚠️  Force killing processes...")
                shago_process.kill()
                playground_process.kill()
            
            print("✅ Cleanup complete")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
