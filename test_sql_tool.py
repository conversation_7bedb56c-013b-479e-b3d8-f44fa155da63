#!/usr/bin/env python3
"""
Test script to verify the SQL tool works correctly.
"""

import sqlite3
from config import DATABASE_FILE

def test_sql_directly():
    """Test SQL queries directly on the database."""
    print("Testing SQL queries directly...")

    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()

    # Test 1: Get categories
    print("\n1. Testing categories query:")
    cursor.execute("SELECT * FROM categories")
    result = cursor.fetchall()
    print(f"Categories: {result}")

    # Test 2: Get inventory
    print("\n2. Testing inventory query:")
    cursor.execute("SELECT * FROM inventory LIMIT 3")
    result = cursor.fetchall()
    print(f"Inventory: {result}")

    # Test 3: Join query
    print("\n3. Testing join query:")
    cursor.execute("""
        SELECT c.title as category, COUNT(i.id) as item_count
        FROM categories c
        LEFT JOIN inventory i ON c.id = i.category_id
        GROUP BY c.id, c.title
    """)
    result = cursor.fetchall()
    print(f"Category counts: {result}")

    conn.close()

def test_sql_tool():
    """Test the SQL tool function."""
    print("\nTesting SQL tool function...")

    # Import the actual function
    from tools.custom_tools import execute_sql_query

    # Test 1: Get categories
    print("\n1. Testing categories query:")
    result = execute_sql_query("SELECT * FROM categories")
    print(f"Result: {result}")

    # Test 2: Get inventory
    print("\n2. Testing inventory query:")
    result = execute_sql_query("SELECT * FROM inventory LIMIT 3")
    print(f"Result: {result}")

if __name__ == "__main__":
    test_sql_directly()
    test_sql_tool()
