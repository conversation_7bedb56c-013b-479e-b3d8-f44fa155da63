#!/usr/bin/env python3
"""
Test script to verify the unified agent works correctly.
"""

from agents.unified_agent import unified_agent

def test_unified_agent():
    """Test the unified agent directly."""
    print("Testing unified agent...")
    
    # Test 1: Database query
    print("\n1. Testing database query:")
    try:
        response = unified_agent.run("What categories do we have in stock?")
        print(f"Response: {response.content}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: General question
    print("\n2. Testing general question:")
    try:
        response = unified_agent.run("Hello, how are you?")
        print(f"Response: {response.content}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_unified_agent()
