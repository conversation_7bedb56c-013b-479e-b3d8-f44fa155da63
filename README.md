# Shago AI Assistant

An offline, multi-modal AI assistant built with the Agno framework. Shago is designed to be a comprehensive, locally-hosted AI assistant that can handle various tasks including database queries, general conversation, and tool-based operations.

## Features

- **Offline-First Architecture**: Runs entirely locally without internet access
- **Multi-Agent System**: Specialized agents for different tasks (SQL, general conversation, tool usage)
- **Two-Pass LLM Approach**: Efficient tool selection and response generation
- **Comprehensive Memory System**: Short, medium, and long-term memory capabilities
- **Retrieval-Augmented Generation (RAG)**: Knowledge base integration for informed responses
- **Custom Tools**: Extensible tool system for various operations
- **Web Interface**: Built-in web UI via Agno Playground

## Architecture

### Core Components

1. **SQL Agent**: Specialized for database queries and natural language to SQL translation
2. **General Agent**: Handles conversational interactions and general questions
3. **Tool User Agent**: Manages tool-based operations and function calling
4. **Memory System**: Persistent memory across sessions with user personalization
5. **Knowledge Base**: RAG system for accessing structured information

### Technology Stack

- **AI Framework**: Agno
- **Local LLMs**: Ollama (<PERSON><PERSON>, SQLCoder, Gorilla OpenFunctions)
- **Database**: SQLite (with PostgreSQL support for production)
- **Vector Database**: PgVector for embeddings
- **Web Framework**: FastAPI via Agno Playground
- **Memory**: SQLite-based persistent storage

## Installation

### Quick Start (Recommended)

1. **Install Ollama**: Download and install from [ollama.com](https://ollama.com/)

2. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd shago_ai_lab
   ```

3. **Full automated setup**:
   ```bash
   make full-setup
   ```

4. **Start Shago**:
   ```bash
   make start
   ```

5. **Access the web interface**:
   Open your browser and navigate to `http://localhost:3023`

### Manual Setup

If you prefer manual setup or don't have `make`:

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Pull Required Models**:
   ```bash
   ollama pull qwen
   ollama pull sqlcoder
   ollama pull gorilla-openfunctions-v2
   ```

3. **Set up database**:
   ```bash
   python setup_database.py
   ```

4. **Verify setup**:
   ```bash
   python test_setup.py
   ```

5. **Start the application**:
   ```bash
   python start_shago.py
   ```

### Available Make Commands

- `make install` - Install Python dependencies
- `make models` - Pull required Ollama models
- `make setup` - Set up database with sample data
- `make test` - Run setup verification tests
- `make start` - Start the Shago assistant
- `make start-local` - Start with local playground
- `make playground` - Start local playground only
- `make clean` - Clean generated files
- `make reset` - Reset database and logs
- `make full-setup` - Complete setup from scratch

## Usage

### Web Interface Options

**Option 1: Advanced Local Playground (Recommended for Offline Use)**
- **URL**: `http://localhost:8080/local_playground.html`
- **Start**: `make start-local` or `make playground`
- **Features**:
  - 🎨 Modern dark theme interface with responsive design
  - 📊 Real-time analytics dashboard with performance metrics
  - 🤖 Multi-agent management and monitoring
  - 🗄️ Interactive database explorer with schema visualization
  - 🛠️ Comprehensive tools integration panel
  - 🧠 Memory system visualization and management
  - 💬 Advanced chat with markdown support and message actions
  - ⚡ Smart agent detection and routing
  - 📱 Mobile-optimized responsive design
- **Best for**: Production use, advanced features, complete offline experience

**Option 2: Agno Playground (Online)**
- **URL**: `https://app.agno.com/playground?endpoint=localhost%3A3023/v1`
- **Features**: Full-featured, cloud-connected interface
- **Best for**: Advanced features, cloud integration

Both interfaces provide:
- Chat with Shago using natural language
- Ask database-related questions
- Request tool-based operations
- View conversation history and memory

### Example Interactions

1. **General Conversation**:
   - "Hello, how are you today?"
   - "Tell me about artificial intelligence"

2. **Database Queries**:
   - "Show me all items in the inventory"
   - "What categories do we have?"

3. **Tool Operations**:
   - "Check the weather in Lagos"
   - "Send a WhatsApp message to John"
   - "Take a note about the meeting"

## Configuration

### Dynamic Context

The system includes dynamic context injection that provides real-time information:
- Shop details (name, address, currency)
- Inventory status
- Exchange rates
- Database schema information

### Memory Settings

- **User Memories**: Enabled for personalization
- **Session Summaries**: Automatic conversation summarization
- **History**: Conversation context maintained across interactions

## Development

### Adding Custom Tools

Create new tools in `tools/custom_tools.py`:

```python
@tool
def my_custom_tool(parameter: str) -> str:
    """Description of what the tool does."""
    # Implementation here
    return result
```

### Extending Agents

Modify agent configurations in the `agents/` directory to:
- Add new tools to existing agents
- Modify instructions and behavior
- Create specialized agents for new domains

### Database Schema

Update `knowledge_base/schema.sql` to reflect your database structure for better SQL generation.

## Production Deployment

For production use:

1. **Switch to PostgreSQL**:
   - Install PostgreSQL with pgvector extension
   - Update connection strings in `main.py`

2. **Enhanced Security**:
   - Add authentication and authorization
   - Implement rate limiting
   - Use HTTPS

3. **Scalability**:
   - Deploy with proper WSGI server (Gunicorn)
   - Use Redis for caching
   - Implement load balancing

## Troubleshooting

### Common Issues

1. **Ollama Connection Error**:
   - Ensure Ollama is running: `ollama serve`
   - Check if models are pulled: `ollama list`
   - Verify Ollama is accessible at `http://localhost:11434`

2. **Missing Models Error**:
   - Run `make models` or manually pull models:
     ```bash
     ollama pull qwen
     ollama pull sqlcoder
     ollama pull gorilla-openfunctions-v2
     ```

3. **Database Errors**:
   - Reset database: `make reset && make setup`
   - Verify SQLite permissions
   - Check database file creation in project directory

4. **Import Errors**:
   - Reinstall dependencies: `make install`
   - Check Python version (3.8+ required)
   - Verify virtual environment activation

5. **Memory Issues**:
   - Monitor system resources
   - Adjust model parameters in `config.py`
   - Consider using smaller models for limited hardware

6. **Port Already in Use**:
   - Change port in `config.py` (SERVER_PORT)
   - Kill existing processes: `lsof -ti:3023 | xargs kill`

### Debug Mode

Enable debug mode by setting `FEATURES["debug_mode"] = True` in `config.py` for more detailed logging.

### Getting Help

1. Run the setup verification: `python test_setup.py`
2. Check the log file: `shago.log`
3. Verify all components with: `make test`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
- Check the [Agno documentation](https://docs.agno.com/)
- Review the troubleshooting section
- Open an issue in the repository
