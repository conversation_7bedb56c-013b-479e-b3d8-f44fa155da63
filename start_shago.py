#!/usr/bin/env python3
"""
Startup script for Shago AI Assistant.
This script performs pre-flight checks and starts the application.
"""

import sys
import os
import subprocess
import logging
from pathlib import Path

def check_ollama():
    """Check if Ollama is running and models are available."""
    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Ollama is not running. Please start it with: ollama serve")
            return False
        
        # Check for required models
        required_models = ["qwen", "sqlcoder", "mistral"]
        available_models = result.stdout.lower()
        
        missing_models = []
        for model in required_models:
            if model.lower() not in available_models:
                missing_models.append(model)
        
        if missing_models:
            print(f"❌ Missing models: {', '.join(missing_models)}")
            print("Pull them with:")
            for model in missing_models:
                print(f"  ollama pull {model}")
            return False
        
        print("✅ Ollama is running with all required models")
        return True
        
    except FileNotFoundError:
        print("❌ Ollama not found. Please install from https://ollama.com/")
        return False
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")
        return False

def check_database():
    """Check if database exists, create if needed."""
    db_file = "shago_offline.db"
    
    if not os.path.exists(db_file):
        print("📦 Database not found. Creating...")
        try:
            import setup_database
            setup_database.setup_database()
            print("✅ Database created successfully")
        except Exception as e:
            print(f"❌ Error creating database: {e}")
            return False
    else:
        print("✅ Database found")
    
    return True

def check_dependencies():
    """Check if required Python packages are installed."""
    try:
        import agno
        import fastapi
        import uvicorn
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Install with: pip install -r requirements.txt")
        return False

def main():
    """Main startup function."""
    print("🚀 Starting Shago AI Assistant...")
    print("=" * 50)
    
    # Run pre-flight checks
    checks = [
        ("Dependencies", check_dependencies),
        ("Ollama", check_ollama),
        ("Database", check_database)
    ]
    
    for check_name, check_func in checks:
        print(f"\n🔍 Checking {check_name}...")
        if not check_func():
            print(f"\n❌ {check_name} check failed. Please fix the issues above.")
            return 1
    
    print("\n" + "=" * 50)
    print("✅ All checks passed! Starting Shago...")
    print("🌐 Web interface will be available at: http://localhost:3023")
    print("📝 Logs will be written to: shago.log")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start the application
    try:
        from main import playground, app
        playground.serve(app="main:app", host="localhost", port=3023, reload=True)
    except KeyboardInterrupt:
        print("\n👋 Shago AI Assistant stopped.")
        return 0
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
