#!/usr/bin/env python3
"""
Test qwen2.5:7b with proper tool calling support and <no-think> flag.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.custom_tools import read_only_sql_executor
from config import MODELS

def test_qwen25_tool_calling():
    """Test qwen2.5:7b with tool calling."""
    print("🔍 Testing qwen2.5:7b with Tool Calling")
    print("=" * 60)
    
    try:
        # Create an agent with qwen2.5 and SQL tool
        qwen25_agent = Agent(
            name="Qwen25_Tool_Agent",
            model=Ollama(id=MODELS["coordinator"]),  # qwen2.5:7b
            tools=[read_only_sql_executor],
            instructions=[
                "<no-think>",  # Disable think mode for better tool calling
                "You are a helpful AI assistant with database access.",
                "When asked about database content, CALL the read_only_sql_executor tool.",
                "For categories: call read_only_sql_executor('SELECT * FROM categories')",
                "For inventory: call read_only_sql_executor('SELECT * FROM inventory')",
                "DO NOT explain what you would do - actually call the function!"
            ]
        )
        
        print("✅ qwen2.5:7b agent created successfully")
        
        # Test database query that should trigger function calling
        print("\n1. Testing database query (should call function):")
        print("Query: 'What categories do we have in stock?'")
        
        try:
            response = qwen25_agent.run("What categories do we have in stock?")
            print(f"Response: {response.content}")
            
            # Check if the response contains actual data vs explanation
            if any(word in response.content.lower() for word in ["electronics", "clothing", "books"]):
                print("🎉 SUCCESS: qwen2.5:7b executed SQL and returned actual data!")
            else:
                print("⚠️  PARTIAL: qwen2.5:7b mentioned SQL but may not have executed it")
                
        except Exception as e:
            print(f"❌ Error with qwen2.5:7b model: {e}")
        
        # Test general conversation (should not call tools)
        print("\n" + "=" * 60)
        print("2. Testing general conversation (should not call tools):")
        print("Query: 'Hello, how are you?'")
        
        try:
            response = qwen25_agent.run("Hello, how are you?")
            print(f"Response: {response.content}")
            
            if "SELECT" not in response.content and "database" not in response.content.lower():
                print("✅ SUCCESS: qwen2.5:7b handled general conversation without calling tools")
            else:
                print("⚠️  qwen2.5:7b unnecessarily mentioned database/SQL for general conversation")
                
        except Exception as e:
            print(f"❌ Error with general conversation: {e}")
            
    except Exception as e:
        print(f"❌ Failed to create qwen2.5:7b agent: {e}")
        print("Make sure the qwen2.5:7b model is fully downloaded:")
        print("ollama pull qwen2.5:7b")

def check_qwen25_availability():
    """Check if qwen2.5:7b model is available."""
    import subprocess
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if 'qwen2.5:7b' in result.stdout:
            print("✅ qwen2.5:7b model is available")
            return True
        else:
            print("❌ qwen2.5:7b model not found. Download with:")
            print("ollama pull qwen2.5:7b")
            return False
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Checking qwen2.5:7b model availability...")
    if check_qwen25_availability():
        test_qwen25_tool_calling()
    else:
        print("\n⏳ Please wait for the qwen2.5:7b model to finish downloading, then run this test again.")
