#!/usr/bin/env python3
"""
Test the qwen2.5 multi-agent system with realistic queries
"""

from qwen25_multi_agent import QwenMultiAgentSystem

def test_system():
    """Test the system with various types of queries"""
    
    print("🚀 Testing Qwen2.5 Multi-Agent System")
    print("=" * 50)
    
    system = QwenMultiAgentSystem()
    
    # Test queries based on actual database schema
    test_queries = [
        # SQL queries (should route to sql_query)
        "Show me all categories in the database",
        "List all inventory items", 
        "Count how many items are in inventory",
        "What categories do we have?",
        
        # Function calls (should route to function_call)
        "What's the weather like in Tokyo?",
        "Search the web for Python tutorials",
        
        # General chat (should route to general_chat)
        "Hello, how are you?",
        "What can you help me with?",
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 Test {i}: {query}")
        print("-" * 40)
        
        result = system.route_request(query)
        
        if result.get("success"):
            print(f"✅ Type: {result.get('type', 'unknown')}")
            print(f"💬 Response: {result.get('content', 'No content')[:200]}...")
            
            # Show SQL query if it's a SQL result
            if result.get('sql_query'):
                print(f"🗃️ SQL: {result.get('sql_query')}")
                
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
        
        print()

if __name__ == "__main__":
    test_system()
