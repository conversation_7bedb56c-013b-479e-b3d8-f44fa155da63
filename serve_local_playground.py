#!/usr/bin/env python3
"""
Simple HTTP server to serve the local Shago playground.
This serves the local HTML interface on a different port.
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def serve_playground(port=8080):
    """Serve the local playground on the specified port."""
    
    # Change to the directory containing the HTML file
    os.chdir(Path(__file__).parent)
    
    # Create handler with CORS support
    class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def do_OPTIONS(self):
            self.send_response(200)
            self.end_headers()
    
    try:
        with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
            print(f"🌐 Local Shago Playground Server")
            print(f"=" * 50)
            print(f"📍 Serving at: http://localhost:{port}")
            print(f"📄 Main file: local_playground.html")
            print(f"🔗 Full URL: http://localhost:{port}/local_playground.html")
            print(f"=" * 50)
            print(f"🚀 Opening browser...")
            print(f"🛑 Press Ctrl+C to stop the server")
            print(f"=" * 50)
            
            # Open browser
            webbrowser.open(f"http://localhost:{port}/local_playground.html")
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n👋 Local playground server stopped.")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use. Try a different port:")
            print(f"   python serve_local_playground.py {port + 1}")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8080.")
    
    serve_playground(port)
