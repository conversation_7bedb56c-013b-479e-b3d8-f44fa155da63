#!/usr/bin/env python3
"""
Test the smart routing agent directly to confirm it works.
"""

from agents.smart_routing_agent import smart_routing_agent

def test_smart_routing_direct():
    """Test the smart routing agent directly."""
    print("🔍 Testing Smart Routing Agent directly...")
    
    try:
        response = smart_routing_agent.run("What categories do we have in stock?")
        print(f"✅ Smart Routing Agent Response:")
        print(response.content)
        
        # Check if it contains actual data
        if any(word in response.content.lower() for word in ["electronics", "clothing", "books"]):
            print("\n🎉 SUCCESS: Smart Routing Agent returned actual database data!")
        else:
            print("\n⚠️  Smart Routing Agent responded but may not have executed SQL")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_smart_routing_direct()
