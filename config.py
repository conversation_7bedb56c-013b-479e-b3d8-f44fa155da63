"""
Configuration file for <PERSON><PERSON>go AI Assistant.
Modify these settings to customize the behavior of your assistant.
"""

# Database Configuration
DATABASE_FILE = "shago_offline.db"
VECTOR_DB_URL = f"sqlite:///{DATABASE_FILE}"

# Server Configuration
SERVER_HOST = "localhost"
SERVER_PORT = 3023
RELOAD_ON_CHANGE = True

# Shop/Business Configuration
SHOP_NAME = "Shago's Emporium"
SHOP_ADDRESS = "123 AI Avenue, Offline City"
DEFAULT_CURRENCY = "NGN"
USD_TO_NAIRA_EXCHANGE_RATE = 1450.00

# Model Configuration
MODELS = {
    "coordinator": "qwen",           # Main coordinating LLM
    "sql_specialist": "sqlcoder",    # SQL generation model
    "tool_specialist": "mistral",    # Function calling model (using mistral as alternative)
    "function_calling": "adrienbrault/gorilla-openfunctions-v2:Q3_K_M"  # Specialized for function calling
}

# Ollama Configuration
OLLAMA_BASE_URL = "http://localhost:11434"  # Default Ollama URL

# Memory Configuration
MEMORY_CONFIG = {
    "enable_user_memories": True,
    "enable_session_summaries": True,
    "max_conversation_history": 50,  # Number of messages to keep in context
}

# Agent Instructions
AGENT_INSTRUCTIONS = {
    "main_team": [
        "You are 'Shago', a team coordinator that MUST delegate all tasks to specialist agents.",
        "NEVER answer questions directly - ALWAYS forward to the appropriate agent.",
        "",
        "DELEGATION RULES:",
        "1. For ANY question about inventory, categories, stock, or database content:",
        "   → Forward to SQL_Agent (member_id: 'sql-agent')",
        "   → Examples: 'What categories?', 'Show inventory', 'List items', 'Stock levels'",
        "",
        "2. For weather, news, notes, calendar, email, WhatsApp:",
        "   → Forward to Tool_User_Agent (member_id: 'tool-user-agent')",
        "",
        "3. For greetings, general chat, AI questions:",
        "   → Forward to General_Agent (member_id: 'general-agent')",
        "",
        "CRITICAL: The user asked about 'categories' - this is a DATABASE question.",
        "You MUST forward this to SQL_Agent immediately.",
        "Do NOT provide your own answer about inventory or categories."
    ],
    "sql_agent": [
        "You are a specialized SQL agent that MUST use the read_only_sql_executor tool for ALL database questions.",
        "When a user asks about inventory, categories, stock, or any database content:",
        "1. Generate the appropriate SQL query",
        "2. Execute it using the read_only_sql_executor tool",
        "3. Present the results in a clear, formatted way",
        "",
        "Available tables:",
        "- inventory (id, title, description, quantity, category_id)",
        "- categories (id, title, description)",
        "",
        "ALWAYS use the tool - never guess or provide generic answers!"
    ],
    "general_agent": [
        "You are a helpful and friendly conversational agent.",
        "Handle general questions, greetings, and non-database/non-tool conversations.",
        "Provide clear and concise answers.",
        "If asked about database content, remind the user to ask about specific data."
    ],
    "tool_user_agent": [
        "You are an expert at using tools to accomplish tasks.",
        "Carefully select the best tool for the user's request.",
        "Always use the appropriate tool rather than providing generic responses."
    ]
}

# Playground Configuration
PLAYGROUND_CONFIG = {
    "name": "Shago Multipurpose Assistant",
    "app_id": "shago-assistant", 
    "description": "An offline, multi-modal AI assistant."
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "shago.log"
}

# Feature Flags
FEATURES = {
    "enable_rag": True,
    "enable_memory": True,
    "enable_context_injection": False,  # Disable to force tool usage
    "enable_session_persistence": True,
    "debug_mode": False
}
