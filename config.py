"""
Configuration file for <PERSON><PERSON>go AI Assistant.
Modify these settings to customize the behavior of your assistant.
"""

# Database Configuration
DATABASE_FILE = "shago_offline.db"
VECTOR_DB_URL = f"sqlite:///{DATABASE_FILE}"

# Server Configuration
SERVER_HOST = "localhost"
SERVER_PORT = 3023
RELOAD_ON_CHANGE = True

# Shop/Business Configuration
SHOP_NAME = "Shago's Emporium"
SHOP_ADDRESS = "123 AI Avenue, Offline City"
DEFAULT_CURRENCY = "NGN"
USD_TO_NAIRA_EXCHANGE_RATE = 1450.00

# Model Configuration
MODELS = {
    "coordinator": "qwen",           # Main coordinating LLM
    "sql_specialist": "sqlcoder",    # SQL generation model
    "tool_specialist": "mistral"     # Function calling model (using mistral as alternative)
}

# Ollama Configuration
OLLAMA_BASE_URL = "http://localhost:11434"  # Default Ollama URL

# Memory Configuration
MEMORY_CONFIG = {
    "enable_user_memories": True,
    "enable_session_summaries": True,
    "max_conversation_history": 50,  # Number of messages to keep in context
}

# Agent Instructions
AGENT_INSTRUCTIONS = {
    "main_team": [
        "You are 'Shago', a highly responsive multipurpose AI assistant.",
        "Your responses should be structured in JSON or Markdown.",
        "If a tool call is needed, delegate to the appropriate agent.",
        "If a user asks a general question, use the general_agent.",
        "If a user asks a database-related question, use the sql_agent.",
        "For all other tool-based requests, use the tool_user_agent."
    ],
    "sql_agent": [
        "You are a specialized agent that translates natural language to SQL queries.",
        "Given a question, generate an SQL query to be executed by the read_only_sql_executor tool.",
        "You must only output the SQL query."
    ],
    "general_agent": [
        "You are a helpful and friendly conversational agent.",
        "Provide clear and concise answers."
    ],
    "tool_user_agent": [
        "You are an expert at using tools to accomplish tasks.",
        "Carefully select the best tool for the user's request."
    ]
}

# Playground Configuration
PLAYGROUND_CONFIG = {
    "name": "Shago Multipurpose Assistant",
    "app_id": "shago-assistant", 
    "description": "An offline, multi-modal AI assistant."
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "shago.log"
}

# Feature Flags
FEATURES = {
    "enable_rag": True,
    "enable_memory": True,
    "enable_context_injection": True,
    "enable_session_persistence": True,
    "debug_mode": False
}
