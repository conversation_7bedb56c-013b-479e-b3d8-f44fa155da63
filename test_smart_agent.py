#!/usr/bin/env python3
"""
Test the smart routing agent.
"""

from agents.smart_routing_agent import smart_routing_agent

def test_smart_agent():
    """Test the smart routing agent."""
    print("Testing smart routing agent...")
    
    # Test 1: Categories query
    print("\n1. Testing categories query:")
    try:
        response = smart_routing_agent.run("What categories do we have in stock?")
        print(f"Response: {response.content}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Inventory query
    print("\n2. Testing inventory query:")
    try:
        response = smart_routing_agent.run("Show me all inventory items")
        print(f"Response: {response.content}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: General question
    print("\n3. Testing general question:")
    try:
        response = smart_routing_agent.run("Hello, how are you?")
        print(f"Response: {response.content}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_smart_agent()
