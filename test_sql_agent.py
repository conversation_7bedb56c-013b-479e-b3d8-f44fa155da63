#!/usr/bin/env python3
"""
Test the original SQL agent to see if it works.
"""

from agents.sql_agent import sql_agent

def test_sql_agent():
    """Test the original SQL agent."""
    print("Testing original SQL agent...")
    
    try:
        response = sql_agent.run("What categories do we have in stock?")
        print(f"Response: {response.content}")
        
        # Check if tools were called
        if hasattr(response, 'tool_calls') and response.tool_calls:
            print(f"Tool calls: {response.tool_calls}")
        else:
            print("No tool calls detected")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sql_agent()
