2025-07-13 08:22:48,438 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:22:48,439 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:16,650 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:23:16,651 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:16,651 - main - INFO - Memory system enabled
2025-07-13 08:23:16,651 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 08:23:16,651 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 08:23:16,651 - main - INFO - Shago team initialized successfully
2025-07-13 08:23:16,651 - main - INFO - Setting up web interface...
2025-07-13 08:23:16,694 - main - INFO - Web interface ready
2025-07-13 08:23:17,671 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 08:23:21,272 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:23:21,272 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:21,272 - main - INFO - Memory system enabled
2025-07-13 08:23:21,272 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 08:23:21,272 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 08:23:21,272 - main - INFO - Shago team initialized successfully
2025-07-13 08:23:21,272 - main - INFO - Setting up web interface...
2025-07-13 08:23:21,314 - main - INFO - Web interface ready
2025-07-13 09:34:08,989 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:34:08,989 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:34:08,989 - main - INFO - Memory system enabled
2025-07-13 09:34:08,990 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:34:08,990 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:34:08,990 - main - INFO - Shago team initialized successfully
2025-07-13 09:34:08,990 - main - INFO - Setting up web interface...
2025-07-13 09:34:09,021 - main - INFO - Web interface ready
2025-07-13 09:34:09,909 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:34:09,981 - watchfiles.main - INFO - 1 change detected
2025-07-13 09:35:12,618 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:35:12,618 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:35:12,618 - main - INFO - Memory system enabled
2025-07-13 09:35:12,619 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:35:12,619 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:35:12,619 - main - INFO - Shago team initialized successfully
2025-07-13 09:35:12,619 - main - INFO - Setting up web interface...
2025-07-13 09:35:12,652 - main - INFO - Web interface ready
2025-07-13 09:35:13,461 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:35:14,088 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:35:14,088 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:35:14,088 - main - INFO - Memory system enabled
2025-07-13 09:35:14,088 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:35:14,088 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:35:14,088 - main - INFO - Shago team initialized successfully
2025-07-13 09:35:14,088 - main - INFO - Setting up web interface...
2025-07-13 09:35:14,121 - main - INFO - Web interface ready
