2025-07-13 08:22:48,438 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:22:48,439 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:16,650 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:23:16,651 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:16,651 - main - INFO - Memory system enabled
2025-07-13 08:23:16,651 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 08:23:16,651 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 08:23:16,651 - main - INFO - Shago team initialized successfully
2025-07-13 08:23:16,651 - main - INFO - Setting up web interface...
2025-07-13 08:23:16,694 - main - INFO - Web interface ready
2025-07-13 08:23:17,671 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 08:23:21,272 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:23:21,272 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:21,272 - main - INFO - Memory system enabled
2025-07-13 08:23:21,272 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 08:23:21,272 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 08:23:21,272 - main - INFO - Shago team initialized successfully
2025-07-13 08:23:21,272 - main - INFO - Setting up web interface...
2025-07-13 08:23:21,314 - main - INFO - Web interface ready
2025-07-13 09:34:08,989 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:34:08,989 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:34:08,989 - main - INFO - Memory system enabled
2025-07-13 09:34:08,990 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:34:08,990 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:34:08,990 - main - INFO - Shago team initialized successfully
2025-07-13 09:34:08,990 - main - INFO - Setting up web interface...
2025-07-13 09:34:09,021 - main - INFO - Web interface ready
2025-07-13 09:34:09,909 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:34:09,981 - watchfiles.main - INFO - 1 change detected
2025-07-13 09:35:12,618 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:35:12,618 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:35:12,618 - main - INFO - Memory system enabled
2025-07-13 09:35:12,619 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:35:12,619 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:35:12,619 - main - INFO - Shago team initialized successfully
2025-07-13 09:35:12,619 - main - INFO - Setting up web interface...
2025-07-13 09:35:12,652 - main - INFO - Web interface ready
2025-07-13 09:35:13,461 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:35:14,088 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:35:14,088 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:35:14,088 - main - INFO - Memory system enabled
2025-07-13 09:35:14,088 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:35:14,088 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:35:14,088 - main - INFO - Shago team initialized successfully
2025-07-13 09:35:14,088 - main - INFO - Setting up web interface...
2025-07-13 09:35:14,121 - main - INFO - Web interface ready
2025-07-13 09:37:25,945 - watchfiles.main - INFO - 1 change detected
2025-07-13 09:37:26,504 - watchfiles.main - INFO - 4 changes detected
2025-07-13 09:37:26,901 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:37:26,901 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:37:26,901 - main - INFO - Memory system enabled
2025-07-13 09:37:26,902 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:37:26,902 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:37:27,014 - watchfiles.main - INFO - 1 change detected
2025-07-13 09:37:42,646 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:37:42,646 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:37:42,646 - main - INFO - Memory system enabled
2025-07-13 09:37:42,646 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:37:42,646 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:18,042 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 09:38:18,042 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:38:18,042 - __main__ - INFO - Memory system enabled
2025-07-13 09:38:18,043 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:38:18,043 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:18,043 - __main__ - INFO - Shago team initialized successfully
2025-07-13 09:38:18,043 - __main__ - INFO - Setting up web interface...
2025-07-13 09:38:18,074 - __main__ - INFO - Web interface ready
2025-07-13 09:38:18,074 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 09:38:19,018 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Memory system enabled
2025-07-13 09:38:19,599 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 09:38:19,631 - __mp_main__ - INFO - Web interface ready
2025-07-13 09:38:19,673 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:38:19,673 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:38:19,673 - main - INFO - Memory system enabled
2025-07-13 09:38:19,673 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:38:19,673 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:19,674 - main - INFO - Shago team initialized successfully
2025-07-13 09:38:19,674 - main - INFO - Setting up web interface...
2025-07-13 09:38:19,702 - main - INFO - Web interface ready
2025-07-13 09:38:19,757 - watchfiles.main - INFO - 4 changes detected
