2025-07-13 08:22:48,438 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:22:48,439 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:16,650 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:23:16,651 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:16,651 - main - INFO - Memory system enabled
2025-07-13 08:23:16,651 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 08:23:16,651 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 08:23:16,651 - main - INFO - Shago team initialized successfully
2025-07-13 08:23:16,651 - main - INFO - Setting up web interface...
2025-07-13 08:23:16,694 - main - INFO - Web interface ready
2025-07-13 08:23:17,671 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 08:23:21,272 - main - INFO - Using database file: shago_offline.db
2025-07-13 08:23:21,272 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 08:23:21,272 - main - INFO - Memory system enabled
2025-07-13 08:23:21,272 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 08:23:21,272 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 08:23:21,272 - main - INFO - Shago team initialized successfully
2025-07-13 08:23:21,272 - main - INFO - Setting up web interface...
2025-07-13 08:23:21,314 - main - INFO - Web interface ready
2025-07-13 09:34:08,989 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:34:08,989 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:34:08,989 - main - INFO - Memory system enabled
2025-07-13 09:34:08,990 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:34:08,990 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:34:08,990 - main - INFO - Shago team initialized successfully
2025-07-13 09:34:08,990 - main - INFO - Setting up web interface...
2025-07-13 09:34:09,021 - main - INFO - Web interface ready
2025-07-13 09:34:09,909 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:34:09,981 - watchfiles.main - INFO - 1 change detected
2025-07-13 09:35:12,618 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:35:12,618 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:35:12,618 - main - INFO - Memory system enabled
2025-07-13 09:35:12,619 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:35:12,619 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:35:12,619 - main - INFO - Shago team initialized successfully
2025-07-13 09:35:12,619 - main - INFO - Setting up web interface...
2025-07-13 09:35:12,652 - main - INFO - Web interface ready
2025-07-13 09:35:13,461 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:35:14,088 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:35:14,088 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:35:14,088 - main - INFO - Memory system enabled
2025-07-13 09:35:14,088 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:35:14,088 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:35:14,088 - main - INFO - Shago team initialized successfully
2025-07-13 09:35:14,088 - main - INFO - Setting up web interface...
2025-07-13 09:35:14,121 - main - INFO - Web interface ready
2025-07-13 09:37:25,945 - watchfiles.main - INFO - 1 change detected
2025-07-13 09:37:26,504 - watchfiles.main - INFO - 4 changes detected
2025-07-13 09:37:26,901 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:37:26,901 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:37:26,901 - main - INFO - Memory system enabled
2025-07-13 09:37:26,902 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:37:26,902 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:37:27,014 - watchfiles.main - INFO - 1 change detected
2025-07-13 09:37:42,646 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:37:42,646 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:37:42,646 - main - INFO - Memory system enabled
2025-07-13 09:37:42,646 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:37:42,646 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:18,042 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 09:38:18,042 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:38:18,042 - __main__ - INFO - Memory system enabled
2025-07-13 09:38:18,043 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:38:18,043 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:18,043 - __main__ - INFO - Shago team initialized successfully
2025-07-13 09:38:18,043 - __main__ - INFO - Setting up web interface...
2025-07-13 09:38:18,074 - __main__ - INFO - Web interface ready
2025-07-13 09:38:18,074 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 09:38:19,018 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Memory system enabled
2025-07-13 09:38:19,599 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 09:38:19,599 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 09:38:19,631 - __mp_main__ - INFO - Web interface ready
2025-07-13 09:38:19,673 - main - INFO - Using database file: shago_offline.db
2025-07-13 09:38:19,673 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 09:38:19,673 - main - INFO - Memory system enabled
2025-07-13 09:38:19,673 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 09:38:19,673 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 09:38:19,674 - main - INFO - Shago team initialized successfully
2025-07-13 09:38:19,674 - main - INFO - Setting up web interface...
2025-07-13 09:38:19,702 - main - INFO - Web interface ready
2025-07-13 09:38:19,757 - watchfiles.main - INFO - 4 changes detected
2025-07-13 09:44:36,728 - watchfiles.main - INFO - 2 changes detected
2025-07-13 09:45:16,839 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 09:45:17,180 - watchfiles.main - INFO - 2 changes detected
2025-07-13 09:45:18,064 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 09:45:35,721 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 09:45:36,412 - watchfiles.main - INFO - 2 changes detected
2025-07-13 09:45:37,349 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 09:46:03,289 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 09:46:03,868 - watchfiles.main - INFO - 2 changes detected
2025-07-13 09:46:04,874 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 09:47:35,020 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 09:47:35,276 - watchfiles.main - INFO - 2 changes detected
2025-07-13 09:47:36,436 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 09:52:42,454 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 09:52:42,989 - watchfiles.main - INFO - 2 changes detected
2025-07-13 09:52:43,768 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 09:53:12,778 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 09:53:14,538 - watchfiles.main - INFO - 2 changes detected
2025-07-13 09:53:15,212 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 10:37:27,064 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:37:42,313 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:37:42,877 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:37:43,498 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 10:37:43,498 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:37:43,498 - __mp_main__ - INFO - Memory system enabled
2025-07-13 10:37:43,499 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:37:43,499 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 10:37:43,499 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 10:37:43,499 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 10:37:43,542 - __mp_main__ - INFO - Web interface ready
2025-07-13 10:37:43,594 - main - INFO - Using database file: shago_offline.db
2025-07-13 10:37:43,594 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:37:43,594 - main - INFO - Memory system enabled
2025-07-13 10:37:43,594 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:37:43,594 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 10:37:43,594 - main - INFO - Shago team initialized successfully
2025-07-13 10:37:43,594 - main - INFO - Setting up web interface...
2025-07-13 10:37:43,633 - main - INFO - Web interface ready
2025-07-13 10:37:43,669 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:37:53,255 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:37:53,804 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:37:54,353 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 10:37:54,354 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:37:54,354 - __mp_main__ - INFO - Memory system enabled
2025-07-13 10:37:54,354 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:37:54,354 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 10:37:54,354 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 10:37:54,354 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 10:37:54,398 - __mp_main__ - INFO - Web interface ready
2025-07-13 10:37:54,443 - main - INFO - Using database file: shago_offline.db
2025-07-13 10:37:54,443 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:37:54,443 - main - INFO - Memory system enabled
2025-07-13 10:37:54,443 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:37:54,443 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 10:37:54,443 - main - INFO - Shago team initialized successfully
2025-07-13 10:37:54,443 - main - INFO - Setting up web interface...
2025-07-13 10:37:54,482 - main - INFO - Web interface ready
2025-07-13 10:37:54,541 - watchfiles.main - INFO - 4 changes detected
2025-07-13 10:38:04,862 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:38:05,374 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:38:05,920 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 10:38:05,920 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:38:05,920 - __mp_main__ - INFO - Memory system enabled
2025-07-13 10:38:05,920 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:38:05,921 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 10:38:05,921 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 10:38:05,921 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 10:38:05,964 - __mp_main__ - INFO - Web interface ready
2025-07-13 10:38:06,010 - main - INFO - Using database file: shago_offline.db
2025-07-13 10:38:06,010 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:38:06,010 - main - INFO - Memory system enabled
2025-07-13 10:38:06,010 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:38:06,010 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 10:38:06,010 - main - INFO - Shago team initialized successfully
2025-07-13 10:38:06,010 - main - INFO - Setting up web interface...
2025-07-13 10:38:06,049 - main - INFO - Web interface ready
2025-07-13 10:38:06,116 - watchfiles.main - INFO - 4 changes detected
2025-07-13 10:39:12,614 - main - INFO - Using database file: shago_offline.db
2025-07-13 10:39:12,615 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:39:12,615 - main - INFO - Memory system enabled
2025-07-13 10:39:12,616 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:39:12,616 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 10:39:12,616 - main - INFO - Shago team initialized successfully
2025-07-13 10:39:12,616 - main - INFO - Setting up web interface...
2025-07-13 10:39:12,678 - main - INFO - Web interface ready
2025-07-13 10:39:13,674 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 10:39:14,845 - main - INFO - Using database file: shago_offline.db
2025-07-13 10:39:14,845 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:39:14,845 - main - INFO - Memory system enabled
2025-07-13 10:39:14,845 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:39:14,845 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 10:39:14,845 - main - INFO - Shago team initialized successfully
2025-07-13 10:39:14,846 - main - INFO - Setting up web interface...
2025-07-13 10:39:14,950 - main - INFO - Web interface ready
2025-07-13 10:39:29,980 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:39:34,972 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:39:35,088 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:39:35,792 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 10:39:48,213 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 10:39:48,213 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:39:48,213 - __main__ - INFO - Memory system enabled
2025-07-13 10:39:48,214 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:39:48,214 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 10:39:48,214 - __main__ - INFO - Shago team initialized successfully
2025-07-13 10:39:48,214 - __main__ - INFO - Setting up web interface...
2025-07-13 10:39:48,257 - __main__ - INFO - Web interface ready
2025-07-13 10:39:48,257 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 10:39:49,198 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 10:39:49,937 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 10:39:49,937 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:39:49,937 - __mp_main__ - INFO - Memory system enabled
2025-07-13 10:39:49,938 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:39:49,938 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 10:39:49,938 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 10:39:49,938 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 10:39:49,980 - __mp_main__ - INFO - Web interface ready
2025-07-13 10:39:50,022 - main - INFO - Using database file: shago_offline.db
2025-07-13 10:39:50,023 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:39:50,023 - main - INFO - Memory system enabled
2025-07-13 10:39:50,023 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:39:50,023 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 10:39:50,023 - main - INFO - Shago team initialized successfully
2025-07-13 10:39:50,023 - main - INFO - Setting up web interface...
2025-07-13 10:39:50,062 - main - INFO - Web interface ready
2025-07-13 10:39:50,111 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:40:17,511 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:40:17,621 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:40:18,341 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 10:40:25,908 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:40:26,010 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:40:26,368 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:40:26,982 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 10:40:27,438 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:40:27,935 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 10:40:27,935 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:40:27,935 - __mp_main__ - INFO - Memory system enabled
2025-07-13 10:40:27,936 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:40:27,936 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 10:40:27,936 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 10:40:27,936 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 10:40:27,979 - __mp_main__ - INFO - Web interface ready
2025-07-13 10:40:28,021 - main - INFO - Using database file: shago_offline.db
2025-07-13 10:40:28,021 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 10:40:28,021 - main - INFO - Memory system enabled
2025-07-13 10:40:28,021 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 10:40:28,021 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 10:40:28,021 - main - INFO - Shago team initialized successfully
2025-07-13 10:40:28,021 - main - INFO - Setting up web interface...
2025-07-13 10:40:28,061 - main - INFO - Web interface ready
2025-07-13 10:40:28,079 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:41:14,396 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:41:14,492 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:41:15,423 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 10:41:33,401 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:41:33,515 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:41:34,540 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 10:49:20,744 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:49:20,885 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:49:32,012 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:49:32,128 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:51:45,702 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:51:45,832 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:51:58,663 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:51:58,790 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:52:05,725 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:52:05,813 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:52:10,472 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:52:10,546 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:52:14,759 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:52:14,874 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:52:26,137 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:52:26,227 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:52:37,210 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:52:37,329 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:52:51,223 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 10:52:51,353 - watchfiles.main - INFO - 2 changes detected
2025-07-13 10:54:56,171 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:55:16,673 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:55:33,577 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:55:52,602 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:56:20,961 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:56:46,665 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:57:13,064 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:57:35,150 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:58:26,541 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:59:10,944 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:59:29,636 - watchfiles.main - INFO - 1 change detected
2025-07-13 10:59:54,286 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:00:23,229 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:00:46,978 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:01:15,262 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:01:33,745 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:02:16,600 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:02:30,068 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:02:45,863 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:03:03,065 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:04:23,806 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:04:36,841 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:04:37,179 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:04:38,274 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:07:00,737 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:07:00,855 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:07:01,987 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:07:55,164 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:07:55,679 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:07:56,303 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:07:56,303 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:07:56,303 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:07:56,304 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:07:56,304 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:07:56,304 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:07:56,304 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:07:56,345 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:07:56,367 - watchfiles.main - INFO - 3 changes detected
2025-07-13 11:07:56,395 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:07:56,395 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:07:56,395 - main - INFO - Memory system enabled
2025-07-13 11:07:56,395 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:07:56,395 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:07:56,395 - main - INFO - Shago team initialized successfully
2025-07-13 11:07:56,395 - main - INFO - Setting up web interface...
2025-07-13 11:07:56,432 - main - INFO - Web interface ready
2025-07-13 11:07:56,727 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:08:23,568 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:08:37,382 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:08:37,915 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:08:38,463 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:08:38,463 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:08:38,463 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:08:38,463 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:08:38,463 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:08:38,463 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:08:38,463 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:08:38,506 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:08:38,553 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:08:38,553 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:08:38,553 - main - INFO - Memory system enabled
2025-07-13 11:08:38,553 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:08:38,553 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:08:38,553 - main - INFO - Shago team initialized successfully
2025-07-13 11:08:38,553 - main - INFO - Setting up web interface...
2025-07-13 11:08:38,593 - main - INFO - Web interface ready
2025-07-13 11:08:38,659 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:08:58,024 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:08:58,025 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:08:58,025 - __main__ - INFO - Memory system enabled
2025-07-13 11:08:58,025 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:08:58,025 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:08:58,025 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:08:58,025 - __main__ - INFO - Setting up web interface...
2025-07-13 11:08:58,069 - __main__ - INFO - Web interface ready
2025-07-13 11:08:58,069 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:08:59,256 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:09:00,055 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:09:00,055 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:09:00,055 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:09:00,055 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:09:00,055 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:09:00,056 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:09:00,056 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:09:00,102 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:09:00,152 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:09:00,152 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:09:00,152 - main - INFO - Memory system enabled
2025-07-13 11:09:00,152 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:09:00,153 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:09:00,153 - main - INFO - Shago team initialized successfully
2025-07-13 11:09:00,153 - main - INFO - Setting up web interface...
2025-07-13 11:09:00,185 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:09:00,194 - main - INFO - Web interface ready
2025-07-13 11:09:15,467 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:09:15,625 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:09:16,448 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:09:33,612 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:09:34,142 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:09:34,665 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:09:34,665 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:09:34,665 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:09:34,665 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:09:34,665 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:09:34,665 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:09:34,665 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:09:34,708 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:09:34,750 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:09:34,750 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:09:34,750 - main - INFO - Memory system enabled
2025-07-13 11:09:34,750 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:09:34,750 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:09:34,750 - main - INFO - Shago team initialized successfully
2025-07-13 11:09:34,750 - main - INFO - Setting up web interface...
2025-07-13 11:09:34,789 - main - INFO - Web interface ready
2025-07-13 11:09:34,826 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:09:49,834 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:09:50,313 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:09:50,825 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:09:50,825 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:09:50,825 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:09:50,825 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:09:50,825 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:09:50,825 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:09:50,825 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:09:50,868 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:09:50,913 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:09:50,913 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:09:50,913 - main - INFO - Memory system enabled
2025-07-13 11:09:50,913 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:09:50,913 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:09:50,913 - main - INFO - Shago team initialized successfully
2025-07-13 11:09:50,913 - main - INFO - Setting up web interface...
2025-07-13 11:09:50,952 - main - INFO - Web interface ready
2025-07-13 11:09:50,991 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:10:01,936 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:10:02,457 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:10:02,986 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:10:02,986 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:10:02,986 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:10:02,986 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:10:02,986 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:10:02,986 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:10:02,986 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:10:03,029 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:10:03,072 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:10:03,072 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:10:03,072 - main - INFO - Memory system enabled
2025-07-13 11:10:03,072 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:10:03,072 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:10:03,072 - main - INFO - Shago team initialized successfully
2025-07-13 11:10:03,072 - main - INFO - Setting up web interface...
2025-07-13 11:10:03,111 - main - INFO - Web interface ready
2025-07-13 11:10:03,137 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:10:33,946 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:10:33,946 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:10:33,946 - __main__ - INFO - Memory system enabled
2025-07-13 11:10:33,946 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:10:33,946 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:10:33,946 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:10:33,946 - __main__ - INFO - Setting up web interface...
2025-07-13 11:10:33,989 - __main__ - INFO - Web interface ready
2025-07-13 11:10:33,989 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:10:35,023 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:10:35,769 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:10:35,769 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:10:35,769 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:10:35,769 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:10:35,769 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:10:35,769 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:10:35,769 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:10:35,814 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:10:35,854 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:10:35,854 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:10:35,854 - main - INFO - Memory system enabled
2025-07-13 11:10:35,854 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:10:35,854 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:10:35,854 - main - INFO - Shago team initialized successfully
2025-07-13 11:10:35,854 - main - INFO - Setting up web interface...
2025-07-13 11:10:35,894 - main - INFO - Web interface ready
2025-07-13 11:10:35,897 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:10:51,513 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:10:51,592 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:10:52,477 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:11:09,999 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:11:10,536 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:11:11,028 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:11:11,028 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:11:11,028 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:11:11,028 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:11:11,028 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:11:11,028 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:11:11,028 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:11:11,072 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:11:11,113 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:11:11,113 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:11:11,113 - main - INFO - Memory system enabled
2025-07-13 11:11:11,113 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:11:11,114 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:11:11,114 - main - INFO - Shago team initialized successfully
2025-07-13 11:11:11,114 - main - INFO - Setting up web interface...
2025-07-13 11:11:11,152 - main - INFO - Web interface ready
2025-07-13 11:11:11,165 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:11:17,570 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:11:18,097 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:11:18,452 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:11:18,452 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:11:18,453 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:11:18,453 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:11:18,453 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:11:18,453 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:11:18,453 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:11:18,495 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:11:18,530 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:11:18,530 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:11:18,530 - main - INFO - Memory system enabled
2025-07-13 11:11:18,531 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:11:18,531 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:11:18,531 - main - INFO - Shago team initialized successfully
2025-07-13 11:11:18,531 - main - INFO - Setting up web interface...
2025-07-13 11:11:18,570 - main - INFO - Web interface ready
2025-07-13 11:11:18,625 - watchfiles.main - INFO - 7 changes detected
2025-07-13 11:11:36,512 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:11:37,025 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:11:38,267 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:11:38,267 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:11:38,268 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:11:38,268 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:11:38,268 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:11:38,268 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:11:38,268 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:11:38,385 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:12:09,132 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:12:09,133 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:12:09,133 - __main__ - INFO - Memory system enabled
2025-07-13 11:12:09,133 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:12:09,133 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:12:09,133 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:12:09,133 - __main__ - INFO - Setting up web interface...
2025-07-13 11:12:51,979 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:12:51,979 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:12:51,979 - __main__ - INFO - Memory system enabled
2025-07-13 11:12:51,980 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:12:51,980 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:12:51,980 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:12:51,980 - __main__ - INFO - Setting up web interface...
2025-07-13 11:12:52,025 - __main__ - INFO - Web interface ready
2025-07-13 11:12:52,025 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:12:53,147 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:12:54,059 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:12:54,059 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:12:54,059 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:12:54,059 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:12:54,059 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:12:54,059 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:12:54,059 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:12:54,103 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:12:54,154 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:12:54,154 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:12:54,154 - main - INFO - Memory system enabled
2025-07-13 11:12:54,154 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:12:54,154 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:12:54,154 - main - INFO - Shago team initialized successfully
2025-07-13 11:12:54,154 - main - INFO - Setting up web interface...
2025-07-13 11:12:54,191 - main - INFO - Web interface ready
2025-07-13 11:12:54,227 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:13:08,766 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:13:08,882 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:13:09,688 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:13:24,312 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:13:24,822 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:13:25,415 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:13:25,415 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:13:25,415 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:13:25,415 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:13:25,416 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:13:25,416 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:13:25,416 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:13:25,504 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:13:39,934 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:13:40,861 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:13:40,862 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:13:40,862 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:13:40,862 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:13:40,862 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:13:40,862 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:13:40,862 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:13:42,247 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:13:42,302 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:13:42,302 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:13:42,302 - main - INFO - Memory system enabled
2025-07-13 11:13:42,302 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:13:42,302 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:13:42,303 - main - INFO - Shago team initialized successfully
2025-07-13 11:13:42,303 - main - INFO - Setting up web interface...
2025-07-13 11:13:42,345 - main - INFO - Web interface ready
2025-07-13 11:13:42,388 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:14:05,101 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:14:05,101 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:14:05,101 - __main__ - INFO - Memory system enabled
2025-07-13 11:14:05,101 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:14:05,101 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:14:05,101 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:14:05,101 - __main__ - INFO - Setting up web interface...
2025-07-13 11:14:05,582 - __main__ - INFO - Web interface ready
2025-07-13 11:14:05,582 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:14:06,528 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:14:07,311 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:14:07,311 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:14:07,311 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:14:07,311 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:14:07,311 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:14:07,311 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:14:07,311 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:14:07,682 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:14:07,730 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:14:07,730 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:14:07,730 - main - INFO - Memory system enabled
2025-07-13 11:14:07,730 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:14:07,730 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:14:07,730 - main - INFO - Shago team initialized successfully
2025-07-13 11:14:07,730 - main - INFO - Setting up web interface...
2025-07-13 11:14:07,778 - main - INFO - Web interface ready
2025-07-13 11:14:07,780 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:14:18,823 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:18,824 - openai._base_client - INFO - Retrying request to /chat/completions in 0.399478 seconds
2025-07-13 11:14:20,017 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:20,018 - openai._base_client - INFO - Retrying request to /chat/completions in 0.885830 seconds
2025-07-13 11:14:21,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:23,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:23,065 - openai._base_client - INFO - Retrying request to /chat/completions in 0.393314 seconds
2025-07-13 11:14:23,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:23,755 - openai._base_client - INFO - Retrying request to /chat/completions in 0.928146 seconds
2025-07-13 11:14:25,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:28,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:28,532 - openai._base_client - INFO - Retrying request to /chat/completions in 0.385204 seconds
2025-07-13 11:14:29,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:29,204 - openai._base_client - INFO - Retrying request to /chat/completions in 0.937564 seconds
2025-07-13 11:14:30,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:35,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:35,304 - openai._base_client - INFO - Retrying request to /chat/completions in 0.428869 seconds
2025-07-13 11:14:36,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:36,022 - openai._base_client - INFO - Retrying request to /chat/completions in 0.944498 seconds
2025-07-13 11:14:37,297 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:14:52,655 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:14:53,169 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:14:53,803 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:14:53,803 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:14:53,803 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:14:53,803 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:14:53,803 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:14:53,803 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:14:53,803 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:14:53,847 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:14:53,896 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:14:53,897 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:14:53,897 - main - INFO - Memory system enabled
2025-07-13 11:14:53,897 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:14:53,897 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:14:53,897 - main - INFO - Shago team initialized successfully
2025-07-13 11:14:53,897 - main - INFO - Setting up web interface...
2025-07-13 11:14:53,937 - main - INFO - Web interface ready
2025-07-13 11:14:54,012 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:15:18,820 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:15:18,821 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:15:18,821 - __main__ - INFO - Memory system enabled
2025-07-13 11:15:18,821 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:15:18,821 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:15:18,821 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:15:18,821 - __main__ - INFO - Setting up web interface...
2025-07-13 11:15:18,865 - __main__ - INFO - Web interface ready
2025-07-13 11:15:18,865 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:15:19,864 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:15:20,634 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:15:20,634 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:15:20,634 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:15:20,634 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:15:20,634 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:15:20,634 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:15:20,634 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:15:20,677 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:15:20,724 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:15:20,724 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:15:20,724 - main - INFO - Memory system enabled
2025-07-13 11:15:20,724 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:15:20,724 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:15:20,724 - main - INFO - Shago team initialized successfully
2025-07-13 11:15:20,724 - main - INFO - Setting up web interface...
2025-07-13 11:15:20,762 - main - INFO - Web interface ready
2025-07-13 11:15:20,779 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:15:36,230 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:15:36,327 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:15:37,137 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:15:49,501 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:15:49,991 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:15:50,618 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:15:50,618 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:15:50,618 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:15:50,618 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:15:50,618 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:15:50,619 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:15:50,619 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:15:50,662 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:15:50,707 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:15:50,707 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:15:50,707 - main - INFO - Memory system enabled
2025-07-13 11:15:50,707 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:15:50,707 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:15:50,707 - main - INFO - Shago team initialized successfully
2025-07-13 11:15:50,707 - main - INFO - Setting up web interface...
2025-07-13 11:15:50,747 - main - INFO - Web interface ready
2025-07-13 11:15:50,788 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:16:11,323 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:16:11,859 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:16:12,420 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:16:12,420 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:16:12,421 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:16:12,421 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:16:12,421 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:16:12,421 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:16:12,421 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:16:12,464 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:16:12,509 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:16:12,509 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:16:12,509 - main - INFO - Memory system enabled
2025-07-13 11:16:12,509 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:16:12,509 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:16:12,509 - main - INFO - Shago team initialized successfully
2025-07-13 11:16:12,509 - main - INFO - Setting up web interface...
2025-07-13 11:16:12,549 - main - INFO - Web interface ready
2025-07-13 11:16:12,601 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:16:22,986 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:16:23,532 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:16:24,089 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:16:24,089 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:16:24,090 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:16:24,090 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:16:24,090 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:16:24,090 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:16:24,090 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:16:24,135 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:16:24,180 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:16:24,180 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:16:24,180 - main - INFO - Memory system enabled
2025-07-13 11:16:24,180 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:16:24,180 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:16:24,180 - main - INFO - Shago team initialized successfully
2025-07-13 11:16:24,181 - main - INFO - Setting up web interface...
2025-07-13 11:16:24,221 - main - INFO - Web interface ready
2025-07-13 11:16:24,222 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:23:24,219 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:23:24,763 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:23:25,335 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:23:25,335 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:23:25,335 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:23:25,335 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:23:25,335 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:23:25,335 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:23:25,336 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:23:25,379 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:23:25,426 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:23:25,426 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:23:25,427 - main - INFO - Memory system enabled
2025-07-13 11:23:25,427 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:23:25,427 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:23:25,427 - main - INFO - Shago team initialized successfully
2025-07-13 11:23:25,427 - main - INFO - Setting up web interface...
2025-07-13 11:23:25,467 - main - INFO - Web interface ready
2025-07-13 11:23:25,498 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:23:59,333 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:23:59,883 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:24:00,514 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:24:00,514 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:24:00,514 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:24:00,514 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:24:00,514 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:24:00,514 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:24:00,514 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:24:00,562 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:24:00,615 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:24:00,615 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:24:00,616 - main - INFO - Memory system enabled
2025-07-13 11:24:00,616 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:24:00,616 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:24:00,616 - main - INFO - Shago team initialized successfully
2025-07-13 11:24:00,616 - main - INFO - Setting up web interface...
2025-07-13 11:24:00,656 - main - INFO - Web interface ready
2025-07-13 11:24:00,668 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:24:26,904 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:24:26,905 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:24:26,905 - __main__ - INFO - Memory system enabled
2025-07-13 11:24:26,905 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:24:26,905 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:24:26,905 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:24:26,905 - __main__ - INFO - Setting up web interface...
2025-07-13 11:24:26,948 - __main__ - INFO - Web interface ready
2025-07-13 11:24:26,948 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:24:28,003 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:24:28,769 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:24:28,769 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:24:28,769 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:24:28,770 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:24:28,770 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:24:28,770 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:24:28,770 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:24:28,813 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:24:28,861 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:24:28,861 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:24:28,861 - main - INFO - Memory system enabled
2025-07-13 11:24:28,861 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:24:28,861 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:24:28,861 - main - INFO - Shago team initialized successfully
2025-07-13 11:24:28,861 - main - INFO - Setting up web interface...
2025-07-13 11:24:28,901 - main - INFO - Web interface ready
2025-07-13 11:24:28,922 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:24:43,339 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:24:43,674 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:24:44,322 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:24:58,238 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:24:58,778 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:24:59,271 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:24:59,271 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:24:59,271 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:24:59,271 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:24:59,271 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:24:59,271 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:24:59,271 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:24:59,359 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:25:12,193 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:25:13,080 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:25:13,080 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:25:13,080 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:25:13,080 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:25:13,081 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:25:13,081 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:25:13,081 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:25:13,540 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:25:13,594 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:25:13,594 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:25:13,594 - main - INFO - Memory system enabled
2025-07-13 11:25:13,594 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:25:13,594 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:25:13,594 - main - INFO - Shago team initialized successfully
2025-07-13 11:25:13,594 - main - INFO - Setting up web interface...
2025-07-13 11:25:13,634 - main - INFO - Web interface ready
2025-07-13 11:25:13,695 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:25:32,477 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:25:32,477 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:25:32,477 - __main__ - INFO - Memory system enabled
2025-07-13 11:25:32,477 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:25:32,477 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:25:32,477 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:25:32,477 - __main__ - INFO - Setting up web interface...
2025-07-13 11:25:32,965 - __main__ - INFO - Web interface ready
2025-07-13 11:25:32,968 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:25:33,874 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:25:34,641 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:25:34,641 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:25:34,641 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:25:34,641 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:25:34,641 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:25:34,641 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:25:34,641 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:25:35,008 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:25:35,055 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:25:35,055 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:25:35,055 - main - INFO - Memory system enabled
2025-07-13 11:25:35,055 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:25:35,055 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:25:35,055 - main - INFO - Shago team initialized successfully
2025-07-13 11:25:35,055 - main - INFO - Setting up web interface...
2025-07-13 11:25:35,096 - main - INFO - Web interface ready
2025-07-13 11:25:35,105 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:25:46,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:46,789 - openai._base_client - INFO - Retrying request to /chat/completions in 0.473114 seconds
2025-07-13 11:25:47,561 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:47,563 - openai._base_client - INFO - Retrying request to /chat/completions in 0.789355 seconds
2025-07-13 11:25:48,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:50,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:50,583 - openai._base_client - INFO - Retrying request to /chat/completions in 0.457265 seconds
2025-07-13 11:25:51,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:51,362 - openai._base_client - INFO - Retrying request to /chat/completions in 0.897386 seconds
2025-07-13 11:25:52,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:54,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:54,998 - openai._base_client - INFO - Retrying request to /chat/completions in 0.408380 seconds
2025-07-13 11:25:55,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:25:55,676 - openai._base_client - INFO - Retrying request to /chat/completions in 0.883661 seconds
2025-07-13 11:25:56,865 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:26:01,301 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:26:01,305 - openai._base_client - INFO - Retrying request to /chat/completions in 0.417914 seconds
2025-07-13 11:26:02,058 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:26:02,058 - openai._base_client - INFO - Retrying request to /chat/completions in 0.857512 seconds
2025-07-13 11:26:03,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 11:26:16,921 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:26:17,443 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:26:18,141 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:26:18,141 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:26:18,141 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:26:18,142 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:26:18,142 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:26:18,142 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:26:18,142 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:26:18,186 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:26:18,237 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:26:18,237 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:26:18,237 - main - INFO - Memory system enabled
2025-07-13 11:26:18,237 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:26:18,237 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:26:18,237 - main - INFO - Shago team initialized successfully
2025-07-13 11:26:18,237 - main - INFO - Setting up web interface...
2025-07-13 11:26:18,277 - main - INFO - Web interface ready
2025-07-13 11:26:18,351 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:27:42,798 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:27:42,798 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:27:42,798 - __main__ - INFO - Memory system enabled
2025-07-13 11:27:42,798 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:27:42,799 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:27:42,799 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:27:42,799 - __main__ - INFO - Setting up web interface...
2025-07-13 11:27:42,842 - __main__ - INFO - Web interface ready
2025-07-13 11:27:42,842 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:27:43,972 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:27:44,748 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:27:44,748 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:27:44,748 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:27:44,748 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:27:44,748 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:27:44,748 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:27:44,748 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:27:44,793 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:27:44,844 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:27:44,844 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:27:44,844 - main - INFO - Memory system enabled
2025-07-13 11:27:44,844 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:27:44,844 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:27:44,844 - main - INFO - Shago team initialized successfully
2025-07-13 11:27:44,844 - main - INFO - Setting up web interface...
2025-07-13 11:27:44,884 - main - INFO - Web interface ready
2025-07-13 11:27:44,937 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:27:59,582 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:27:59,660 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:28:00,446 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:28:14,796 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:28:15,290 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:28:15,916 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:28:15,916 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:28:15,917 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:28:15,917 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:28:15,917 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:28:15,917 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:28:15,917 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:28:15,961 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:28:16,058 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:28:16,059 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:28:16,059 - main - INFO - Memory system enabled
2025-07-13 11:28:16,059 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:28:16,059 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:28:16,059 - main - INFO - Shago team initialized successfully
2025-07-13 11:28:16,059 - main - INFO - Setting up web interface...
2025-07-13 11:28:16,104 - main - INFO - Web interface ready
2025-07-13 11:28:16,135 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:28:39,661 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:28:39,661 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:28:39,661 - __main__ - INFO - Memory system enabled
2025-07-13 11:28:39,661 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:28:39,661 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:28:39,661 - __main__ - INFO - Shago team initialized successfully
2025-07-13 11:28:39,661 - __main__ - INFO - Setting up web interface...
2025-07-13 11:28:39,705 - __main__ - INFO - Web interface ready
2025-07-13 11:28:39,705 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 11:28:40,574 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 11:28:41,345 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:28:41,345 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:28:41,345 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:28:41,346 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:28:41,346 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:28:41,346 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:28:41,346 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:28:41,390 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:28:41,438 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:28:41,438 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:28:41,438 - main - INFO - Memory system enabled
2025-07-13 11:28:41,438 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:28:41,438 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:28:41,438 - main - INFO - Shago team initialized successfully
2025-07-13 11:28:41,438 - main - INFO - Setting up web interface...
2025-07-13 11:28:41,478 - main - INFO - Web interface ready
2025-07-13 11:28:41,490 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:28:54,377 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 11:28:54,458 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:28:55,329 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 11:29:08,684 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:29:09,185 - watchfiles.main - INFO - 2 changes detected
2025-07-13 11:29:09,702 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:29:09,702 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:29:09,702 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:29:09,702 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:29:09,702 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:29:09,702 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:29:09,702 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:29:09,792 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:29:09,840 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:29:09,840 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:29:09,840 - main - INFO - Memory system enabled
2025-07-13 11:29:09,840 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:29:09,840 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:29:09,840 - main - INFO - Shago team initialized successfully
2025-07-13 11:29:09,840 - main - INFO - Setting up web interface...
2025-07-13 11:29:09,880 - main - INFO - Web interface ready
2025-07-13 11:29:09,918 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:29:45,278 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:29:45,783 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:29:46,373 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:29:46,373 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:29:46,373 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:29:46,373 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:29:46,373 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:29:46,374 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:29:46,374 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:29:46,418 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:29:46,469 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:29:46,470 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:29:46,470 - main - INFO - Memory system enabled
2025-07-13 11:29:46,470 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:29:46,470 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:29:46,470 - main - INFO - Shago team initialized successfully
2025-07-13 11:29:46,470 - main - INFO - Setting up web interface...
2025-07-13 11:29:46,513 - main - INFO - Web interface ready
2025-07-13 11:29:46,513 - watchfiles.main - INFO - 4 changes detected
2025-07-13 11:30:18,235 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:30:18,770 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:30:19,356 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:30:19,356 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:30:19,356 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:30:19,357 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:30:19,357 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:30:19,357 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:30:19,357 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:30:19,436 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:30:19,487 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:30:19,487 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:30:19,487 - main - INFO - Memory system enabled
2025-07-13 11:30:19,487 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:30:19,487 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:30:19,487 - main - INFO - Shago team initialized successfully
2025-07-13 11:30:19,487 - main - INFO - Setting up web interface...
2025-07-13 11:30:19,527 - main - INFO - Web interface ready
2025-07-13 11:30:19,550 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:30:39,517 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:30:40,033 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:30:40,602 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:30:40,602 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:30:40,602 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:30:40,603 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:30:40,603 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:30:40,603 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:30:40,603 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:30:40,647 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:30:40,697 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:30:40,697 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:30:40,697 - main - INFO - Memory system enabled
2025-07-13 11:30:40,697 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:30:40,697 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:30:40,697 - main - INFO - Shago team initialized successfully
2025-07-13 11:30:40,697 - main - INFO - Setting up web interface...
2025-07-13 11:30:40,736 - main - INFO - Web interface ready
2025-07-13 11:30:40,768 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:31:31,308 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:31:31,830 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:31:32,426 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:31:32,427 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:31:32,427 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:31:32,427 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:31:32,427 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:31:32,427 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:31:32,427 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:31:32,470 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:31:32,524 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:31:32,524 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:31:32,524 - main - INFO - Memory system enabled
2025-07-13 11:31:32,524 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:31:32,524 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:31:32,524 - main - INFO - Shago team initialized successfully
2025-07-13 11:31:32,524 - main - INFO - Setting up web interface...
2025-07-13 11:31:32,563 - main - INFO - Web interface ready
2025-07-13 11:31:32,565 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:31:43,662 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:31:44,159 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:31:44,721 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:31:44,721 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:31:44,721 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:31:44,721 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:31:44,721 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:31:44,721 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:31:44,721 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:31:44,765 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:31:44,811 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:31:44,811 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:31:44,811 - main - INFO - Memory system enabled
2025-07-13 11:31:44,811 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:31:44,811 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:31:44,811 - main - INFO - Shago team initialized successfully
2025-07-13 11:31:44,811 - main - INFO - Setting up web interface...
2025-07-13 11:31:44,851 - main - INFO - Web interface ready
2025-07-13 11:31:44,897 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:31:50,033 - watchfiles.main - INFO - 3 changes detected
2025-07-13 11:32:12,436 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:32:12,929 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:32:13,477 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:32:13,477 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:32:13,477 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:32:13,478 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:32:13,478 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:32:13,478 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:32:13,478 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:32:13,522 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:32:13,566 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:32:13,566 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:32:13,566 - main - INFO - Memory system enabled
2025-07-13 11:32:13,567 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:32:13,567 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:32:13,567 - main - INFO - Shago team initialized successfully
2025-07-13 11:32:13,567 - main - INFO - Setting up web interface...
2025-07-13 11:32:13,606 - main - INFO - Web interface ready
2025-07-13 11:32:13,612 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:32:27,424 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:32:27,929 - watchfiles.main - INFO - 1 change detected
2025-07-13 11:32:28,471 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 11:32:28,471 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:32:28,471 - __mp_main__ - INFO - Memory system enabled
2025-07-13 11:32:28,471 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:32:28,471 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 11:32:28,471 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 11:32:28,471 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 11:32:28,516 - __mp_main__ - INFO - Web interface ready
2025-07-13 11:32:28,560 - main - INFO - Using database file: shago_offline.db
2025-07-13 11:32:28,561 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 11:32:28,561 - main - INFO - Memory system enabled
2025-07-13 11:32:28,561 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 11:32:28,561 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 11:32:28,561 - main - INFO - Shago team initialized successfully
2025-07-13 11:32:28,561 - main - INFO - Setting up web interface...
2025-07-13 11:32:28,600 - main - INFO - Web interface ready
2025-07-13 11:32:28,608 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:38:38,993 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:38:39,118 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:38:40,497 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:38:51,747 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:38:51,849 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:38:52,864 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:39:04,883 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:39:04,997 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:39:06,460 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:39:13,591 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:39:13,674 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:39:15,011 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:39:58,924 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:39:59,524 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:40:00,249 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:40:00,249 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:40:00,250 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:40:00,250 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:40:00,250 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:40:00,250 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:40:00,250 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:40:00,298 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:40:00,351 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:40:00,351 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:40:00,351 - main - INFO - Memory system enabled
2025-07-13 12:40:00,351 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:40:00,351 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:40:00,351 - main - INFO - Shago team initialized successfully
2025-07-13 12:40:00,351 - main - INFO - Setting up web interface...
2025-07-13 12:40:00,391 - main - INFO - Web interface ready
2025-07-13 12:40:00,439 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:40:12,375 - watchfiles.main - INFO - 3 changes detected
2025-07-13 12:40:27,292 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:40:27,762 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:40:28,300 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:40:28,301 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:40:28,301 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:40:28,301 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:40:28,301 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:40:28,301 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:40:28,301 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:40:28,344 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:40:28,391 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:40:28,391 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:40:28,391 - main - INFO - Memory system enabled
2025-07-13 12:40:28,391 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:40:28,391 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:40:28,391 - main - INFO - Shago team initialized successfully
2025-07-13 12:40:28,391 - main - INFO - Setting up web interface...
2025-07-13 12:40:28,431 - main - INFO - Web interface ready
2025-07-13 12:40:28,506 - watchfiles.main - INFO - 4 changes detected
2025-07-13 12:40:45,211 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:40:45,707 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:40:46,239 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:40:46,239 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:40:46,239 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:40:46,240 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:40:46,240 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:40:46,240 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:40:46,240 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:40:46,738 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:40:46,785 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:40:46,786 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:40:46,786 - main - INFO - Memory system enabled
2025-07-13 12:40:46,786 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:40:46,786 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:40:46,786 - main - INFO - Shago team initialized successfully
2025-07-13 12:40:46,786 - main - INFO - Setting up web interface...
2025-07-13 12:40:46,827 - main - INFO - Web interface ready
2025-07-13 12:40:46,873 - watchfiles.main - INFO - 4 changes detected
2025-07-13 12:41:04,538 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:41:04,538 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:41:04,538 - __main__ - INFO - Memory system enabled
2025-07-13 12:41:04,538 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:41:04,539 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:41:04,539 - __main__ - INFO - Shago team initialized successfully
2025-07-13 12:41:04,539 - __main__ - INFO - Setting up web interface...
2025-07-13 12:41:04,994 - __main__ - INFO - Web interface ready
2025-07-13 12:41:04,994 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 12:41:06,439 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 12:41:07,270 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:41:07,270 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:41:07,270 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:41:07,270 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:41:07,270 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:41:07,270 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:41:07,270 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:41:07,644 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:41:07,689 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:41:07,689 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:41:07,689 - main - INFO - Memory system enabled
2025-07-13 12:41:07,689 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:41:07,689 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:41:07,689 - main - INFO - Shago team initialized successfully
2025-07-13 12:41:07,690 - main - INFO - Setting up web interface...
2025-07-13 12:41:07,729 - main - INFO - Web interface ready
2025-07-13 12:41:07,766 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:41:24,691 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:41:25,209 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:41:25,746 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:41:25,746 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:41:25,746 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:41:25,746 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:41:25,746 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:41:25,746 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:41:25,747 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:41:25,790 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:41:25,837 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:41:25,837 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:41:25,837 - main - INFO - Memory system enabled
2025-07-13 12:41:25,837 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:41:25,837 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:41:25,837 - main - INFO - Shago team initialized successfully
2025-07-13 12:41:25,837 - main - INFO - Setting up web interface...
2025-07-13 12:41:25,877 - main - INFO - Web interface ready
2025-07-13 12:41:25,946 - watchfiles.main - INFO - 4 changes detected
2025-07-13 12:41:38,608 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:41:38,685 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:41:39,606 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:41:52,430 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:41:52,986 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:41:53,460 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:41:53,460 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:41:53,460 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:41:53,460 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:41:53,460 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:41:53,460 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:41:53,460 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:41:53,502 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:41:53,544 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:41:53,544 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:41:53,544 - main - INFO - Memory system enabled
2025-07-13 12:41:53,545 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:41:53,545 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:41:53,545 - main - INFO - Shago team initialized successfully
2025-07-13 12:41:53,545 - main - INFO - Setting up web interface...
2025-07-13 12:41:53,583 - main - INFO - Web interface ready
2025-07-13 12:41:53,622 - watchfiles.main - INFO - 4 changes detected
2025-07-13 12:42:07,804 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:42:07,900 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:42:08,904 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:42:46,102 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:42:46,617 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:42:47,142 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:42:47,142 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:42:47,142 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:42:47,143 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:42:47,143 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:42:47,143 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:42:47,143 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:42:47,253 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:43:01,392 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:43:02,241 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:43:02,241 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:43:02,241 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:43:02,241 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:43:02,241 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:43:02,241 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:43:02,241 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:43:02,351 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:44:55,339 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:44:56,335 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:44:56,335 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:44:56,335 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:44:56,335 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:44:56,335 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:44:56,336 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:44:56,336 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:44:56,818 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:44:56,875 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:44:56,875 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:44:56,875 - main - INFO - Memory system enabled
2025-07-13 12:44:56,876 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:44:56,876 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:44:56,876 - main - INFO - Shago team initialized successfully
2025-07-13 12:44:56,876 - main - INFO - Setting up web interface...
2025-07-13 12:44:56,917 - main - INFO - Web interface ready
2025-07-13 12:44:56,947 - watchfiles.main - INFO - 4 changes detected
2025-07-13 12:45:09,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:09,431 - openai._base_client - INFO - Retrying request to /chat/completions in 0.403266 seconds
2025-07-13 12:45:10,423 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:10,424 - openai._base_client - INFO - Retrying request to /chat/completions in 0.839655 seconds
2025-07-13 12:45:11,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:13,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:13,552 - openai._base_client - INFO - Retrying request to /chat/completions in 0.447657 seconds
2025-07-13 12:45:14,319 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:14,319 - openai._base_client - INFO - Retrying request to /chat/completions in 0.912653 seconds
2025-07-13 12:45:15,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:17,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:17,984 - openai._base_client - INFO - Retrying request to /chat/completions in 0.377398 seconds
2025-07-13 12:45:18,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:18,827 - openai._base_client - INFO - Retrying request to /chat/completions in 0.783003 seconds
2025-07-13 12:45:20,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:24,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:24,668 - openai._base_client - INFO - Retrying request to /chat/completions in 0.450499 seconds
2025-07-13 12:45:25,776 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:25,777 - openai._base_client - INFO - Retrying request to /chat/completions in 0.844735 seconds
2025-07-13 12:45:26,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 12:45:40,173 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:45:40,690 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:45:41,285 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:45:41,285 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:45:41,285 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:45:41,286 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:45:41,286 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:45:41,286 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:45:41,286 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:45:41,330 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:45:41,378 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:45:41,378 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:45:41,378 - main - INFO - Memory system enabled
2025-07-13 12:45:41,379 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:45:41,379 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:45:41,379 - main - INFO - Shago team initialized successfully
2025-07-13 12:45:41,379 - main - INFO - Setting up web interface...
2025-07-13 12:45:41,418 - main - INFO - Web interface ready
2025-07-13 12:45:41,478 - watchfiles.main - INFO - 4 changes detected
2025-07-13 12:45:54,696 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:45:54,779 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:45:56,017 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:46:28,391 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:46:28,870 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:46:29,418 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:46:29,418 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:46:29,418 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:46:29,418 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:46:29,418 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:46:29,418 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:46:29,418 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:46:29,461 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:46:29,508 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:46:29,509 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:46:29,509 - main - INFO - Memory system enabled
2025-07-13 12:46:29,509 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:46:29,509 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:46:29,509 - main - INFO - Shago team initialized successfully
2025-07-13 12:46:29,509 - main - INFO - Setting up web interface...
2025-07-13 12:46:29,547 - main - INFO - Web interface ready
2025-07-13 12:46:29,553 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:47:23,081 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:47:23,171 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:47:24,292 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:47:31,970 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:47:32,046 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:47:33,269 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:47:41,844 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:47:41,844 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:47:41,844 - main - INFO - Memory system enabled
2025-07-13 12:47:41,845 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:47:41,845 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:47:41,845 - main - INFO - Shago team initialized successfully
2025-07-13 12:47:41,845 - main - INFO - Setting up web interface...
2025-07-13 12:47:41,889 - main - INFO - Web interface ready
2025-07-13 12:47:42,850 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 12:47:42,940 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:48:35,955 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:48:35,956 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:48:35,956 - __main__ - INFO - Memory system enabled
2025-07-13 12:48:35,956 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:48:35,956 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:48:35,956 - __main__ - INFO - Shago team initialized successfully
2025-07-13 12:48:35,956 - __main__ - INFO - Setting up web interface...
2025-07-13 12:48:36,000 - __main__ - INFO - Web interface ready
2025-07-13 12:48:36,000 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 12:48:37,058 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 12:48:37,910 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:48:37,910 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:48:37,910 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:48:37,910 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:48:37,910 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:48:37,910 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:48:37,910 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:48:37,954 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:48:38,003 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:48:38,003 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:48:38,003 - main - INFO - Memory system enabled
2025-07-13 12:48:38,003 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:48:38,003 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:48:38,003 - main - INFO - Shago team initialized successfully
2025-07-13 12:48:38,003 - main - INFO - Setting up web interface...
2025-07-13 12:48:38,029 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:48:38,044 - main - INFO - Web interface ready
2025-07-13 12:48:50,812 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:48:53,827 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:48:53,912 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:48:54,977 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:49:03,721 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:49:13,043 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:49:13,150 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:49:14,080 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:49:22,026 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:49:22,156 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:49:23,414 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:49:32,265 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:49:32,377 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:49:33,625 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 12:49:37,684 - watchfiles.main - INFO - 1 change detected
2025-07-13 12:49:38,219 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:49:38,791 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 12:49:38,791 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:49:38,791 - __mp_main__ - INFO - Memory system enabled
2025-07-13 12:49:38,791 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:49:38,792 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 12:49:38,792 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 12:49:38,792 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 12:49:38,837 - __mp_main__ - INFO - Web interface ready
2025-07-13 12:49:38,888 - main - INFO - Using database file: shago_offline.db
2025-07-13 12:49:38,888 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 12:49:38,888 - main - INFO - Memory system enabled
2025-07-13 12:49:38,889 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 12:49:38,889 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 12:49:38,889 - main - INFO - Shago team initialized successfully
2025-07-13 12:49:38,889 - main - INFO - Setting up web interface...
2025-07-13 12:49:38,930 - main - INFO - Web interface ready
2025-07-13 12:49:38,957 - watchfiles.main - INFO - 4 changes detected
2025-07-13 12:49:53,828 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 12:49:53,910 - watchfiles.main - INFO - 2 changes detected
2025-07-13 12:49:54,956 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:00:18,291 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:00:18,384 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:00:19,493 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:00:24,359 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:00:24,449 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:00:25,451 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:00:30,953 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:00:31,041 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:00:32,199 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:00:42,972 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:00:43,110 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:00:44,313 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:00:57,472 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:00:57,592 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:00:58,707 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:03:40,322 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:03:40,421 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:03:41,677 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:03:59,649 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:03:59,747 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:04:01,189 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:05:08,330 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:05:08,697 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:05:09,337 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:07:13,949 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:07:34,874 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:13:53,371 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:13:53,371 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:13:53,371 - main - INFO - Memory system enabled
2025-07-13 15:13:53,371 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:13:53,371 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:13:53,372 - main - INFO - Shago team initialized successfully
2025-07-13 15:13:53,372 - main - INFO - Setting up web interface...
2025-07-13 15:13:53,414 - main - INFO - Web interface ready
2025-07-13 15:13:54,514 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 15:13:54,676 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:15:19,381 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:15:19,382 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:15:19,382 - main - INFO - Memory system enabled
2025-07-13 15:15:19,382 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:15:19,382 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:15:19,382 - main - INFO - Shago team initialized successfully
2025-07-13 15:15:19,382 - main - INFO - Setting up web interface...
2025-07-13 15:15:19,427 - main - INFO - Web interface ready
2025-07-13 15:15:20,496 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 15:15:21,303 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:15:21,303 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:15:21,303 - main - INFO - Memory system enabled
2025-07-13 15:15:21,303 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:15:21,303 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:15:21,303 - main - INFO - Shago team initialized successfully
2025-07-13 15:15:21,303 - main - INFO - Setting up web interface...
2025-07-13 15:15:21,346 - main - INFO - Web interface ready
2025-07-13 15:16:35,073 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:17:01,006 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:25:00,189 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:25:00,189 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:25:00,189 - __main__ - INFO - Memory system enabled
2025-07-13 15:25:00,190 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:25:00,190 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:25:00,190 - __main__ - INFO - Shago team initialized successfully
2025-07-13 15:25:00,190 - __main__ - INFO - Setting up web interface...
2025-07-13 15:25:00,256 - __main__ - INFO - Web interface ready
2025-07-13 15:25:00,256 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 15:25:01,452 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 15:25:02,226 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:25:02,226 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:25:02,226 - __mp_main__ - INFO - Memory system enabled
2025-07-13 15:25:02,226 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:25:02,226 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:25:02,226 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 15:25:02,226 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 15:25:02,269 - __mp_main__ - INFO - Web interface ready
2025-07-13 15:25:02,319 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:25:02,319 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:25:02,319 - main - INFO - Memory system enabled
2025-07-13 15:25:02,319 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:25:02,319 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:25:02,319 - main - INFO - Shago team initialized successfully
2025-07-13 15:25:02,319 - main - INFO - Setting up web interface...
2025-07-13 15:25:02,358 - main - INFO - Web interface ready
2025-07-13 15:25:02,430 - watchfiles.main - INFO - 4 changes detected
2025-07-13 15:25:18,744 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 500 Internal Server Error"
2025-07-13 15:27:58,471 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:27:59,034 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:27:59,649 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:27:59,649 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:27:59,649 - __mp_main__ - INFO - Memory system enabled
2025-07-13 15:27:59,649 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:27:59,650 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:27:59,650 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 15:27:59,650 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 15:27:59,693 - __mp_main__ - INFO - Web interface ready
2025-07-13 15:27:59,741 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:27:59,741 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:27:59,742 - main - INFO - Memory system enabled
2025-07-13 15:27:59,742 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:27:59,742 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:27:59,742 - main - INFO - Shago team initialized successfully
2025-07-13 15:27:59,742 - main - INFO - Setting up web interface...
2025-07-13 15:27:59,776 - watchfiles.main - INFO - 4 changes detected
2025-07-13 15:27:59,781 - main - INFO - Web interface ready
2025-07-13 15:28:10,388 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:28:10,872 - watchfiles.main - INFO - 1 change detected
2025-07-13 15:28:11,267 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:28:11,267 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:28:11,267 - __mp_main__ - INFO - Memory system enabled
2025-07-13 15:28:11,267 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:28:11,267 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:28:11,267 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 15:28:11,267 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 15:28:11,311 - __mp_main__ - INFO - Web interface ready
2025-07-13 15:28:11,347 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:28:11,347 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:28:11,347 - main - INFO - Memory system enabled
2025-07-13 15:28:11,347 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:28:11,347 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:28:11,347 - main - INFO - Shago team initialized successfully
2025-07-13 15:28:11,347 - main - INFO - Setting up web interface...
2025-07-13 15:28:11,387 - main - INFO - Web interface ready
2025-07-13 15:28:11,454 - watchfiles.main - INFO - 4 changes detected
2025-07-13 15:28:27,758 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 15:28:27,846 - watchfiles.main - INFO - 2 changes detected
2025-07-13 15:28:28,915 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 15:58:24,622 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:58:24,622 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:58:24,622 - __main__ - INFO - Memory system enabled
2025-07-13 15:58:24,623 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:58:24,623 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:58:24,623 - __main__ - INFO - Shago team initialized successfully
2025-07-13 15:58:24,623 - __main__ - INFO - Setting up web interface...
2025-07-13 15:58:24,999 - __main__ - INFO - Web interface ready
2025-07-13 15:58:24,999 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 15:58:26,120 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 15:58:26,700 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:58:26,700 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:58:26,700 - __mp_main__ - INFO - Memory system enabled
2025-07-13 15:58:26,700 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:58:26,700 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:58:26,700 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 15:58:26,700 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 15:58:26,998 - __mp_main__ - INFO - Web interface ready
2025-07-13 15:58:27,048 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:58:27,048 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:58:27,048 - main - INFO - Memory system enabled
2025-07-13 15:58:27,048 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:58:27,048 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:58:27,048 - main - INFO - Shago team initialized successfully
2025-07-13 15:58:27,048 - main - INFO - Setting up web interface...
2025-07-13 15:58:27,078 - main - INFO - Web interface ready
2025-07-13 15:58:27,164 - watchfiles.main - INFO - 4 changes detected
2025-07-13 15:58:41,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 15:58:41,009 - openai._base_client - INFO - Retrying request to /chat/completions in 0.431926 seconds
2025-07-13 15:58:42,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 15:58:42,799 - openai._base_client - INFO - Retrying request to /chat/completions in 0.759339 seconds
2025-07-13 15:59:50,800 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:59:50,801 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:59:50,801 - __main__ - INFO - Memory system enabled
2025-07-13 15:59:50,801 - __main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:59:50,801 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:59:50,801 - __main__ - INFO - Shago team initialized successfully
2025-07-13 15:59:50,801 - __main__ - INFO - Setting up web interface...
2025-07-13 15:59:50,833 - __main__ - INFO - Web interface ready
2025-07-13 15:59:50,833 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 15:59:52,067 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 15:59:52,659 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 15:59:52,659 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:59:52,659 - __mp_main__ - INFO - Memory system enabled
2025-07-13 15:59:52,659 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:59:52,659 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 15:59:52,660 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 15:59:52,660 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 15:59:52,689 - __mp_main__ - INFO - Web interface ready
2025-07-13 15:59:52,742 - main - INFO - Using database file: shago_offline.db
2025-07-13 15:59:52,742 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 15:59:52,742 - main - INFO - Memory system enabled
2025-07-13 15:59:52,742 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 15:59:52,742 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 15:59:52,742 - main - INFO - Shago team initialized successfully
2025-07-13 15:59:52,742 - main - INFO - Setting up web interface...
2025-07-13 15:59:52,770 - main - INFO - Web interface ready
2025-07-13 15:59:52,839 - watchfiles.main - INFO - 4 changes detected
2025-07-13 16:00:08,029 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 16:00:08,127 - watchfiles.main - INFO - 2 changes detected
2025-07-13 16:00:09,034 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 16:00:21,845 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:00:22,383 - watchfiles.main - INFO - 2 changes detected
2025-07-13 16:00:22,731 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:00:22,731 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:00:22,732 - __mp_main__ - INFO - Memory system enabled
2025-07-13 16:00:22,732 - __mp_main__ - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 16:00:22,732 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:00:22,732 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:00:22,732 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:00:22,763 - __mp_main__ - INFO - Web interface ready
2025-07-13 16:00:22,800 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:00:22,800 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:00:22,800 - main - INFO - Memory system enabled
2025-07-13 16:00:22,800 - main - WARNING - Could not load knowledge base: PgVector.__init__() got an unexpected keyword argument 'collection'
2025-07-13 16:00:22,800 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:00:22,800 - main - INFO - Shago team initialized successfully
2025-07-13 16:00:22,800 - main - INFO - Setting up web interface...
2025-07-13 16:00:22,828 - main - INFO - Web interface ready
2025-07-13 16:00:22,850 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:03:19,140 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:03:19,663 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:03:20,271 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:03:20,271 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:03:20,271 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:03:20,271 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:03:20,271 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:03:20,314 - __mp_main__ - INFO - Web interface ready
2025-07-13 16:03:20,362 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:03:20,362 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:03:20,362 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:03:20,363 - main - INFO - Shago team initialized successfully
2025-07-13 16:03:20,363 - main - INFO - Setting up web interface...
2025-07-13 16:03:20,401 - watchfiles.main - INFO - 4 changes detected
2025-07-13 16:03:20,403 - main - INFO - Web interface ready
2025-07-13 16:03:46,621 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:03:46,622 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:03:46,622 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:03:46,622 - __main__ - INFO - Shago team initialized successfully
2025-07-13 16:03:46,622 - __main__ - INFO - Setting up web interface...
2025-07-13 16:03:46,664 - __main__ - INFO - Web interface ready
2025-07-13 16:03:46,664 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 16:03:47,693 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 16:03:48,470 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:03:48,470 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:03:48,470 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:03:48,470 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:03:48,471 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:03:48,512 - __mp_main__ - INFO - Web interface ready
2025-07-13 16:03:48,577 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:03:48,577 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:03:48,577 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:03:48,577 - main - INFO - Shago team initialized successfully
2025-07-13 16:03:48,577 - main - INFO - Setting up web interface...
2025-07-13 16:03:48,616 - main - INFO - Web interface ready
2025-07-13 16:03:48,618 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:04:04,626 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 16:04:05,617 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 16:04:24,381 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:04:24,902 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:04:25,502 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:04:25,502 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:04:25,502 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:04:25,502 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:04:25,502 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:04:25,983 - __mp_main__ - INFO - Web interface ready
2025-07-13 16:04:26,030 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:04:26,030 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:04:26,030 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:04:26,030 - main - INFO - Shago team initialized successfully
2025-07-13 16:04:26,030 - main - INFO - Setting up web interface...
2025-07-13 16:04:26,070 - main - INFO - Web interface ready
2025-07-13 16:04:26,125 - watchfiles.main - INFO - 4 changes detected
2025-07-13 16:04:39,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:39,076 - openai._base_client - INFO - Retrying request to /chat/completions in 0.390355 seconds
2025-07-13 16:04:40,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:40,275 - openai._base_client - INFO - Retrying request to /chat/completions in 0.806148 seconds
2025-07-13 16:04:41,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:42,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:42,978 - openai._base_client - INFO - Retrying request to /chat/completions in 0.421131 seconds
2025-07-13 16:04:44,090 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:44,092 - openai._base_client - INFO - Retrying request to /chat/completions in 0.861441 seconds
2025-07-13 16:04:45,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:48,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:48,241 - openai._base_client - INFO - Retrying request to /chat/completions in 0.495314 seconds
2025-07-13 16:04:49,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:49,078 - openai._base_client - INFO - Retrying request to /chat/completions in 0.988289 seconds
2025-07-13 16:04:50,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:55,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:55,079 - openai._base_client - INFO - Retrying request to /chat/completions in 0.424512 seconds
2025-07-13 16:04:55,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:04:55,858 - openai._base_client - INFO - Retrying request to /chat/completions in 0.803562 seconds
2025-07-13 16:04:57,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:05:19,353 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:05:19,858 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:05:20,443 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:05:20,443 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:05:20,443 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:05:20,443 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:05:20,443 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:05:20,486 - __mp_main__ - INFO - Web interface ready
2025-07-13 16:05:20,536 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:05:20,536 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:05:20,536 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:05:20,537 - main - INFO - Shago team initialized successfully
2025-07-13 16:05:20,537 - main - INFO - Setting up web interface...
2025-07-13 16:05:20,574 - main - INFO - Web interface ready
2025-07-13 16:05:20,653 - watchfiles.main - INFO - 4 changes detected
2025-07-13 16:05:33,224 - httpx - INFO - HTTP Request: POST http://127.0.0.1:11434/api/chat "HTTP/1.1 200 OK"
2025-07-13 16:05:34,257 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/team-runs "HTTP/1.1 201 Created"
2025-07-13 16:05:47,410 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:05:47,942 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:05:48,350 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:05:48,350 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:05:48,350 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:05:48,351 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:05:48,351 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:05:48,462 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:08:31,088 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:08:32,018 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:08:32,018 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:08:32,019 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:08:32,019 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:08:32,019 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:08:32,493 - __mp_main__ - INFO - Web interface ready
2025-07-13 16:08:32,561 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:08:32,561 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:08:32,561 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:08:32,561 - main - INFO - Shago team initialized successfully
2025-07-13 16:08:32,561 - main - INFO - Setting up web interface...
2025-07-13 16:08:32,600 - main - INFO - Web interface ready
2025-07-13 16:08:32,631 - watchfiles.main - INFO - 4 changes detected
2025-07-13 16:08:33,407 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:33,409 - openai._base_client - INFO - Retrying request to /chat/completions in 0.472090 seconds
2025-07-13 16:08:34,187 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:34,190 - openai._base_client - INFO - Retrying request to /chat/completions in 0.770409 seconds
2025-07-13 16:08:35,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:36,924 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:36,925 - openai._base_client - INFO - Retrying request to /chat/completions in 0.449465 seconds
2025-07-13 16:08:37,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:37,724 - openai._base_client - INFO - Retrying request to /chat/completions in 0.815846 seconds
2025-07-13 16:08:38,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:41,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:41,432 - openai._base_client - INFO - Retrying request to /chat/completions in 0.479833 seconds
2025-07-13 16:08:42,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:42,211 - openai._base_client - INFO - Retrying request to /chat/completions in 0.761523 seconds
2025-07-13 16:08:43,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:47,850 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:47,851 - openai._base_client - INFO - Retrying request to /chat/completions in 0.375479 seconds
2025-07-13 16:08:48,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:48,519 - openai._base_client - INFO - Retrying request to /chat/completions in 0.971380 seconds
2025-07-13 16:08:49,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:56,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:56,553 - openai._base_client - INFO - Retrying request to /chat/completions in 0.455564 seconds
2025-07-13 16:08:57,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:08:57,321 - openai._base_client - INFO - Retrying request to /chat/completions in 0.908366 seconds
2025-07-13 16:08:58,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:00,088 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:00,089 - openai._base_client - INFO - Retrying request to /chat/completions in 0.436179 seconds
2025-07-13 16:09:00,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:00,829 - openai._base_client - INFO - Retrying request to /chat/completions in 0.838285 seconds
2025-07-13 16:09:01,930 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:04,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:04,879 - openai._base_client - INFO - Retrying request to /chat/completions in 0.491676 seconds
2025-07-13 16:09:05,697 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:05,699 - openai._base_client - INFO - Retrying request to /chat/completions in 0.812402 seconds
2025-07-13 16:09:06,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:11,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:11,372 - openai._base_client - INFO - Retrying request to /chat/completions in 0.405032 seconds
2025-07-13 16:09:12,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:09:12,075 - openai._base_client - INFO - Retrying request to /chat/completions in 0.993970 seconds
2025-07-13 16:09:13,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:12:54,914 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:12:54,914 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:12:54,914 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:12:54,914 - main - INFO - Shago team initialized successfully
2025-07-13 16:12:54,914 - main - INFO - Setting up web interface...
2025-07-13 16:12:55,374 - main - INFO - Web interface ready
2025-07-13 16:12:56,521 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 16:12:57,308 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:12:57,308 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:12:57,308 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:12:57,308 - main - INFO - Shago team initialized successfully
2025-07-13 16:12:57,308 - main - INFO - Setting up web interface...
2025-07-13 16:12:57,679 - main - INFO - Web interface ready
2025-07-13 16:14:10,598 - __main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:14:10,598 - __main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:14:10,598 - __main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:14:10,598 - __main__ - INFO - Shago team initialized successfully
2025-07-13 16:14:10,598 - __main__ - INFO - Setting up web interface...
2025-07-13 16:14:11,048 - __main__ - INFO - Web interface ready
2025-07-13 16:14:11,048 - __main__ - INFO - Starting Shago AI Assistant on localhost:3023
2025-07-13 16:14:11,936 - httpx - INFO - HTTP Request: POST https://api.agno.com/v2/apps "HTTP/1.1 202 Accepted"
2025-07-13 16:14:12,704 - __mp_main__ - INFO - Using database file: shago_offline.db
2025-07-13 16:14:12,704 - __mp_main__ - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:14:12,704 - __mp_main__ - INFO - Initializing Shago multi-agent system...
2025-07-13 16:14:12,704 - __mp_main__ - INFO - Shago team initialized successfully
2025-07-13 16:14:12,704 - __mp_main__ - INFO - Setting up web interface...
2025-07-13 16:14:13,074 - __mp_main__ - INFO - Web interface ready
2025-07-13 16:14:13,114 - main - INFO - Using database file: shago_offline.db
2025-07-13 16:14:13,114 - main - INFO - Vector database URL: sqlite:///shago_offline.db
2025-07-13 16:14:13,114 - main - INFO - Initializing Shago multi-agent system...
2025-07-13 16:14:13,114 - main - INFO - Shago team initialized successfully
2025-07-13 16:14:13,114 - main - INFO - Setting up web interface...
2025-07-13 16:14:13,152 - main - INFO - Web interface ready
2025-07-13 16:14:13,192 - watchfiles.main - INFO - 1 change detected
2025-07-13 16:14:20,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:20,126 - openai._base_client - INFO - Retrying request to /chat/completions in 0.420126 seconds
2025-07-13 16:14:21,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:21,416 - openai._base_client - INFO - Retrying request to /chat/completions in 0.923888 seconds
2025-07-13 16:14:23,123 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:24,818 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:24,820 - openai._base_client - INFO - Retrying request to /chat/completions in 0.441681 seconds
2025-07-13 16:14:25,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:25,612 - openai._base_client - INFO - Retrying request to /chat/completions in 0.822009 seconds
2025-07-13 16:14:26,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:29,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:29,564 - openai._base_client - INFO - Retrying request to /chat/completions in 0.462193 seconds
2025-07-13 16:14:30,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:30,338 - openai._base_client - INFO - Retrying request to /chat/completions in 0.878044 seconds
2025-07-13 16:14:31,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:36,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:36,362 - openai._base_client - INFO - Retrying request to /chat/completions in 0.401990 seconds
2025-07-13 16:14:37,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:37,404 - openai._base_client - INFO - Retrying request to /chat/completions in 0.765371 seconds
2025-07-13 16:14:38,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:46,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:46,512 - openai._base_client - INFO - Retrying request to /chat/completions in 0.470166 seconds
2025-07-13 16:14:47,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:47,679 - openai._base_client - INFO - Retrying request to /chat/completions in 0.884993 seconds
2025-07-13 16:14:48,873 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:50,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:50,823 - openai._base_client - INFO - Retrying request to /chat/completions in 0.437867 seconds
2025-07-13 16:14:51,978 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:51,980 - openai._base_client - INFO - Retrying request to /chat/completions in 0.952839 seconds
2025-07-13 16:14:53,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:56,076 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:56,078 - openai._base_client - INFO - Retrying request to /chat/completions in 0.416422 seconds
2025-07-13 16:14:56,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:14:56,841 - openai._base_client - INFO - Retrying request to /chat/completions in 0.977961 seconds
2025-07-13 16:14:58,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:03,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:03,091 - openai._base_client - INFO - Retrying request to /chat/completions in 0.379857 seconds
2025-07-13 16:15:03,913 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:03,914 - openai._base_client - INFO - Retrying request to /chat/completions in 0.841002 seconds
2025-07-13 16:15:05,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:18,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:18,904 - openai._base_client - INFO - Retrying request to /chat/completions in 0.403782 seconds
2025-07-13 16:15:19,629 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:19,630 - openai._base_client - INFO - Retrying request to /chat/completions in 0.946165 seconds
2025-07-13 16:15:20,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:22,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:22,306 - openai._base_client - INFO - Retrying request to /chat/completions in 0.441289 seconds
2025-07-13 16:15:23,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:23,552 - openai._base_client - INFO - Retrying request to /chat/completions in 0.875423 seconds
2025-07-13 16:15:24,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:27,441 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:27,442 - openai._base_client - INFO - Retrying request to /chat/completions in 0.473459 seconds
2025-07-13 16:15:28,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:28,356 - openai._base_client - INFO - Retrying request to /chat/completions in 0.914804 seconds
2025-07-13 16:15:29,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:34,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:34,238 - openai._base_client - INFO - Retrying request to /chat/completions in 0.498269 seconds
2025-07-13 16:15:35,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-13 16:15:35,090 - openai._base_client - INFO - Retrying request to /chat/completions in 0.960478 seconds
2025-07-13 16:15:36,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
