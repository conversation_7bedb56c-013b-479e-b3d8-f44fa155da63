from agno.tools import tool
from datetime import datetime
import sqlite3
import logging
from config import DATABASE_FILE

logger = logging.getLogger(__name__)

@tool
def get_current_datetime() -> str:
    """Returns the current date and time."""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

@tool
def read_only_sql_executor(sql_query: str) -> str:
    """Executes a read-only SQL query on the local SQLite database."""
    try:
        # Basic security check - only allow SELECT statements
        query_upper = sql_query.strip().upper()
        if not query_upper.startswith('SELECT'):
            return "Error: Only SELECT queries are allowed for security reasons."

        con = sqlite3.connect(DATABASE_FILE)
        cur = con.cursor()
        res = cur.execute(sql_query)
        results = res.fetchall()

        logger.info(f"Executed SQL query: {sql_query[:100]}...")
        return str(results)
    except Exception as e:
        error_msg = f"Error executing query: {e}"
        logger.error(error_msg)
        return error_msg
    finally:
        if con:
            con.close()

# --- Placeholder for other custom tools ---
@tool
def news_browser(topic: str) -> str:
    """Browses the web for news on a given topic."""
    # In a real offline scenario, this would search a pre-downloaded news archive.
    return f"Simulating search for '{topic}': [Offline News] Major tech conference announced."

@tool
def weather_checker(city: str) -> str:
    """Checks the weather for a given city."""
    # Offline: This would query a local weather data cache.
    return f"The weather in {city} is currently sunny (from offline data)."

# The following tools would require more complex implementations
# involving message queues or background processes for offline use.

@tool
def whatsapp_messenger(recipient: str, message: str) -> str:
    """Sends a WhatsApp message."""
    return f"Message to {recipient} queued for sending: '{message}'"

@tool
def print_executor(content: str) -> str:
    """Prints a document."""
    return f"Printing document with content: '{content}'"

@tool
def inventory_manager(action: str, item: str, quantity: int = 1) -> str:
    """Manages inventory."""
    return f"Inventory action '{action}' for item '{item}' ({quantity}) has been queued."

@tool
def note_taker(note: str) -> str:
    """Takes a note."""
    return f"Note taken: '{note}'"

@tool
def calendar_manager(action: str, event_details: dict) -> str:
    """Manages calendar events."""
    return f"Calendar action '{action}' for event '{event_details.get('summary')}' has been queued."

@tool
def email_manager(action: str, email_details: dict) -> str:
    """Manages emails."""
    return f"Email action '{action}' for recipient '{email_details.get('to')}' has been queued."
