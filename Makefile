# Shago AI Assistant - Makefile

.PHONY: help install setup test start start-local playground clean reset models

# Default target
help:
	@echo "Shago AI Assistant - Available Commands:"
	@echo "========================================"
	@echo "install    - Install Python dependencies"
	@echo "models     - Pull required Ollama models"
	@echo "setup      - Set up database with sample data"
	@echo "test       - Run setup verification tests"
	@echo "start      - Start the Shago assistant"
	@echo "start-local- Start with advanced local playground"
	@echo "playground - Start advanced local playground only"
	@echo "demo       - Start with advanced playground and open browser"
	@echo "clean      - Clean generated files"
	@echo "reset      - Reset database and logs"
	@echo "help       - Show this help message"

# Install Python dependencies
install:
	@echo "Installing Python dependencies..."
	pip install -r requirements.txt
	@echo "✅ Dependencies installed"

# Pull required Ollama models
models:
	@echo "Pulling required Ollama models..."
	ollama pull qwen
	ollama pull sqlcoder
	ollama pull gorilla-openfunctions-v2
	@echo "✅ Models downloaded"

# Set up database
setup:
	@echo "Setting up database..."
	python setup_database.py
	@echo "✅ Database setup complete"

# Run tests
test:
	@echo "Running setup verification..."
	python test_setup.py

# Start the application
start:
	@echo "Starting Shago AI Assistant..."
	python start_shago.py

# Start with local playground
start-local:
	@echo "Starting Shago with local playground..."
	@echo "This will start both the AI assistant and local web interface"
	python start_shago.py &
	sleep 3
	python serve_local_playground.py

# Start advanced local playground only (assumes Shago is already running)
playground:
	@echo "Starting advanced local playground..."
	@echo "Make sure Shago is running on port 3023"
	python serve_local_playground.py

# Demo mode - start everything and open browser
demo:
	@echo "Starting Shago AI Assistant Demo..."
	@echo "This will start both services and open the advanced playground"
	python start_with_local_playground.py

# Clean generated files
clean:
	@echo "Cleaning generated files..."
	rm -f *.log
	rm -rf __pycache__
	rm -rf agents/__pycache__
	rm -rf tools/__pycache__
	find . -name "*.pyc" -delete
	find . -name "*.pyo" -delete
	@echo "✅ Cleanup complete"

# Reset database and logs
reset: clean
	@echo "Resetting database and logs..."
	rm -f *.db
	rm -f *.sqlite
	rm -f *.log
	@echo "✅ Reset complete"

# Full setup from scratch
full-setup: install models setup test
	@echo "✅ Full setup complete! Run 'make start' to launch Shago."
