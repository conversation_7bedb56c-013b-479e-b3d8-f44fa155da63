#!/usr/bin/env python3
"""
Test script to create a simple agent with tools and test it.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.custom_tools import read_only_sql_executor
from config import MODELS

def test_simple_agent():
    """Test a simple agent with SQL tool."""
    print("Creating simple agent with SQL tool...")
    
    # Create a simple agent with just the SQL tool
    simple_agent = Agent(
        name="Simple_SQL_Agent",
        model=Ollama(id=MODELS["coordinator"]),
        tools=[read_only_sql_executor],
        instructions=[
            "You are a database assistant.",
            "When asked about categories or inventory, use the read_only_sql_executor tool.",
            "For the question 'What categories do we have?', call read_only_sql_executor('SELECT * FROM categories')",
            "Always use the tool - do not just describe what you would do."
        ]
    )
    
    print("Testing simple agent...")
    try:
        response = simple_agent.run("What categories do we have in stock?")
        print(f"Response: {response.content}")
        
        # Check if tools were called
        if hasattr(response, 'tool_calls') and response.tool_calls:
            print(f"Tool calls: {response.tool_calls}")
        else:
            print("No tool calls detected")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_agent()
