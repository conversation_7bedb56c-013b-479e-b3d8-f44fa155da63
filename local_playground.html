<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shago AI Assistant - Advanced Local Playground</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/5.1.1/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-surface-2: #334155;
            --light-text: #f8fafc;
            --muted-text: #94a3b8;
            --border-color: #475569;
            --shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--dark-bg);
            color: var(--light-text);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
            background: var(--dark-bg);
        }
        
        /* Sidebar */
        .sidebar {
            width: 300px;
            background: var(--dark-surface);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 600;
        }

        .logo i {
            color: var(--primary-color);
            font-size: 24px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--muted-text);
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .sidebar-toggle:hover {
            background: var(--dark-surface-2);
            color: var(--light-text);
        }

        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            padding: 0 20px 10px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--muted-text);
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--muted-text);
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .nav-item:hover {
            background: var(--dark-surface-2);
            color: var(--light-text);
        }

        .nav-item.active {
            background: var(--primary-color);
            color: white;
        }

        .nav-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .nav-item-text {
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-item-text,
        .sidebar.collapsed .nav-section-title,
        .sidebar.collapsed .logo span {
            opacity: 0;
            pointer-events: none;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--dark-bg);
        }
        
        .header {
            background: var(--dark-surface);
            border-bottom: 1px solid var(--border-color);
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 70px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--light-text);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--muted-text);
            font-size: 14px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-stats {
            display: flex;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--light-text);
        }

        .stat-label {
            font-size: 12px;
            color: var(--muted-text);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-bar {
            background: var(--dark-surface);
            border-bottom: 1px solid var(--border-color);
            padding: 12px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-indicator.connected {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-indicator.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status-indicator.connecting {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-pulse {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .agent-status {
            display: flex;
            gap: 12px;
        }

        .agent-chip {
            padding: 4px 8px;
            background: var(--dark-surface-2);
            border-radius: 12px;
            font-size: 11px;
            color: var(--muted-text);
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .agent-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            background: var(--dark-bg);
            scroll-behavior: smooth;
        }

        .messages::-webkit-scrollbar {
            width: 6px;
        }

        .messages::-webkit-scrollbar-track {
            background: var(--dark-surface);
        }

        .messages::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .message {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: var(--primary-color);
            color: white;
        }

        .message.assistant .message-avatar {
            background: var(--dark-surface-2);
            color: var(--light-text);
        }

        .message.system .message-avatar {
            background: var(--warning-color);
            color: var(--dark-bg);
        }

        .message-wrapper {
            flex: 1;
            max-width: 70%;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;
            font-size: 12px;
            color: var(--muted-text);
        }

        .message-sender {
            font-weight: 500;
        }

        .message-time {
            opacity: 0.7;
        }

        .message-content {
            background: var(--dark-surface);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 16px;
            word-wrap: break-word;
            line-height: 1.5;
            position: relative;
        }

        .message.user .message-content {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .message.system .message-content {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
        }

        .message-content pre {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            overflow-x: auto;
            font-size: 13px;
        }

        .message-content code {
            background: var(--dark-bg);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 13px;
        }

        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            background: none;
            border: none;
            color: var(--muted-text);
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: var(--dark-surface-2);
            color: var(--light-text);
        }
        
        .typing-indicator {
            display: none;
            padding: 16px 24px;
            color: var(--muted-text);
            font-style: italic;
            animation: fadeInUp 0.3s ease;
        }

        .typing-dots {
            display: inline-flex;
            gap: 4px;
            margin-left: 8px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--muted-text);
            animation: typingDots 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingDots {
            0%, 60%, 100% { opacity: 0.3; }
            30% { opacity: 1; }
        }

        .input-container {
            background: var(--dark-surface);
            border-top: 1px solid var(--border-color);
            padding: 20px 24px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: flex-end;
            gap: 12px;
            background: var(--dark-bg);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 12px;
            transition: border-color 0.2s ease;
        }

        .input-wrapper:focus-within {
            border-color: var(--primary-color);
        }

        .input-field {
            flex: 1;
            background: none;
            border: none;
            outline: none;
            color: var(--light-text);
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            min-height: 20px;
            max-height: 120px;
            overflow-y: auto;
        }

        .input-field::placeholder {
            color: var(--muted-text);
        }

        .input-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .input-btn {
            background: none;
            border: none;
            color: var(--muted-text);
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .input-btn:hover {
            background: var(--dark-surface-2);
            color: var(--light-text);
        }

        .send-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .send-button:hover:not(:disabled) {
            background: var(--secondary-color);
            transform: translateY(-1px);
        }

        .send-button:disabled {
            background: var(--dark-surface-2);
            color: var(--muted-text);
            cursor: not-allowed;
            transform: none;
        }

        .send-button.loading {
            pointer-events: none;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .quick-actions {
            padding: 16px 24px;
            background: var(--dark-surface);
            border-top: 1px solid var(--border-color);
        }

        .quick-actions-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .quick-actions-title {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--muted-text);
            letter-spacing: 0.5px;
        }

        .quick-actions-toggle {
            background: none;
            border: none;
            color: var(--muted-text);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .quick-actions-toggle:hover {
            background: var(--dark-surface-2);
            color: var(--light-text);
        }

        .example-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .example-category {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            transition: all 0.2s ease;
        }

        .example-category:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .category-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .category-icon.sql { background: rgba(59, 130, 246, 0.1); color: var(--primary-color); }
        .category-icon.general { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
        .category-icon.tools { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }

        .category-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--light-text);
        }

        .example-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .example-btn {
            background: none;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 8px 12px;
            color: var(--light-text);
            cursor: pointer;
            font-size: 12px;
            text-align: left;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .example-btn:hover {
            background: var(--dark-surface-2);
            border-color: var(--primary-color);
            transform: translateX(4px);
        }

        .example-btn i {
            opacity: 0.7;
        }

        /* Panels */
        .panel {
            display: none;
            flex: 1;
            flex-direction: column;
            background: var(--dark-bg);
        }

        .panel.active {
            display: flex;
        }

        .panel-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            background: var(--dark-surface);
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--light-text);
            margin-bottom: 4px;
        }

        .panel-subtitle {
            color: var(--muted-text);
            font-size: 14px;
        }

        .panel-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        /* Stats and Metrics */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .metric-card {
            background: var(--dark-surface);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .metric-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--light-text);
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 12px;
            color: var(--muted-text);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-change {
            font-size: 11px;
            margin-top: 4px;
        }

        .metric-change.positive { color: var(--success-color); }
        .metric-change.negative { color: var(--error-color); }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -300px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .header-stats {
                display: none;
            }

            .example-categories {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .message-wrapper {
                max-width: 85%;
            }

            .input-container {
                padding: 16px;
            }
        }

        /* Utilities */
        .hidden { display: none !important; }
        .fade-in { animation: fadeIn 0.3s ease; }
        .slide-up { animation: slideUp 0.3s ease; }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Tooltips */
        .tooltip {
            position: relative;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark-bg);
            color: var(--light-text);
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <span>Shago AI</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <button class="nav-item active" data-panel="chat">
                        <i class="fas fa-comments"></i>
                        <span class="nav-item-text">Chat</span>
                    </button>
                    <button class="nav-item" data-panel="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span class="nav-item-text">Analytics</span>
                    </button>
                    <button class="nav-item" data-panel="agents">
                        <i class="fas fa-users"></i>
                        <span class="nav-item-text">Agents</span>
                    </button>
                    <button class="nav-item" data-panel="database">
                        <i class="fas fa-database"></i>
                        <span class="nav-item-text">Database</span>
                    </button>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Tools</div>
                    <button class="nav-item" data-panel="tools">
                        <i class="fas fa-tools"></i>
                        <span class="nav-item-text">Tools</span>
                    </button>
                    <button class="nav-item" data-panel="memory">
                        <i class="fas fa-brain"></i>
                        <span class="nav-item-text">Memory</span>
                    </button>
                    <button class="nav-item" data-panel="settings">
                        <i class="fas fa-cog"></i>
                        <span class="nav-item-text">Settings</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div class="header-left">
                    <div class="page-title" id="pageTitle">Chat Interface</div>
                    <div class="breadcrumb">
                        <span>Shago AI</span>
                        <i class="fas fa-chevron-right"></i>
                        <span id="currentPanel">Chat</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="messageCount">0</div>
                            <div class="stat-label">Messages</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="sessionTime">00:00</div>
                            <div class="stat-label">Session</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="activeAgent">General</div>
                            <div class="stat-label">Active Agent</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <div class="status-indicator connecting" id="connectionStatus">
                        <div class="status-pulse"></div>
                        <span>Connecting...</span>
                    </div>
                    <div class="agent-status" id="agentStatus">
                        <div class="agent-chip" data-agent="sql">
                            <i class="fas fa-database"></i>
                            SQL
                        </div>
                        <div class="agent-chip" data-agent="general">
                            <i class="fas fa-comment"></i>
                            General
                        </div>
                        <div class="agent-chip" data-agent="tools">
                            <i class="fas fa-tools"></i>
                            Tools
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Panel -->
            <div class="panel active" id="chatPanel">
                <div class="chat-container">
                    <div class="messages" id="messages">
                        <div class="message system">
                            <div class="message-avatar">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="message-wrapper">
                                <div class="message-header">
                                    <span class="message-sender">System</span>
                                    <span class="message-time" id="welcomeTime"></span>
                                </div>
                                <div class="message-content">
                                    <strong>Welcome to Shago AI Assistant!</strong><br><br>
                                    I'm your advanced offline AI assistant with specialized agents:
                                    <br>• <strong>🗄️ SQL Agent</strong>: Database queries and data analysis
                                    <br>• <strong>💬 General Agent</strong>: Conversations and general questions
                                    <br>• <strong>🛠️ Tool Agent</strong>: Various operations and integrations
                                    <br><br>
                                    <em>Try the quick examples below or ask me anything!</em>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="typing-indicator" id="typingIndicator">
                        <i class="fas fa-robot"></i>
                        Shago is thinking
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <div class="quick-actions-header">
                        <div class="quick-actions-title">Quick Examples</div>
                        <button class="quick-actions-toggle" id="quickActionsToggle">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                    <div class="example-categories" id="exampleCategories">
                        <div class="example-category">
                            <div class="category-header">
                                <div class="category-icon sql">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="category-title">Database Queries</div>
                            </div>
                            <div class="example-buttons">
                                <button class="example-btn" onclick="sendExample('Show me all inventory items')">
                                    <i class="fas fa-box"></i>
                                    Show all inventory
                                </button>
                                <button class="example-btn" onclick="sendExample('What categories do we have in stock?')">
                                    <i class="fas fa-list"></i>
                                    List categories
                                </button>
                                <button class="example-btn" onclick="sendExample('Which items have low stock?')">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Low stock items
                                </button>
                            </div>
                        </div>

                        <div class="example-category">
                            <div class="category-header">
                                <div class="category-icon general">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <div class="category-title">General Chat</div>
                            </div>
                            <div class="example-buttons">
                                <button class="example-btn" onclick="sendExample('Hello! How are you today?')">
                                    <i class="fas fa-hand-wave"></i>
                                    Say hello
                                </button>
                                <button class="example-btn" onclick="sendExample('Tell me about artificial intelligence')">
                                    <i class="fas fa-brain"></i>
                                    About AI
                                </button>
                                <button class="example-btn" onclick="sendExample('What can you help me with?')">
                                    <i class="fas fa-question-circle"></i>
                                    Your capabilities
                                </button>
                            </div>
                        </div>

                        <div class="example-category">
                            <div class="category-header">
                                <div class="category-icon tools">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <div class="category-title">Tool Operations</div>
                            </div>
                            <div class="example-buttons">
                                <button class="example-btn" onclick="sendExample('Check the weather in Lagos')">
                                    <i class="fas fa-cloud-sun"></i>
                                    Check weather
                                </button>
                                <button class="example-btn" onclick="sendExample('Take a note: Team meeting at 3pm tomorrow')">
                                    <i class="fas fa-sticky-note"></i>
                                    Take a note
                                </button>
                                <button class="example-btn" onclick="sendExample('Browse news about technology')">
                                    <i class="fas fa-newspaper"></i>
                                    Browse news
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Input Container -->
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea
                            class="input-field"
                            id="messageInput"
                            placeholder="Type your message to Shago..."
                            rows="1"
                        ></textarea>
                        <div class="input-actions">
                            <button class="input-btn tooltip" data-tooltip="Attach file" type="button">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button class="input-btn tooltip" data-tooltip="Voice input" type="button">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <button class="send-button" id="sendButton" type="submit">
                                <span class="send-text">Send</span>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Panel -->
            <div class="panel" id="analyticsPanel">
                <div class="panel-header">
                    <div class="panel-title">Analytics Dashboard</div>
                    <div class="panel-subtitle">Performance metrics and usage statistics</div>
                </div>
                <div class="panel-content">
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value" id="totalMessages">0</div>
                            <div class="metric-label">Total Messages</div>
                            <div class="metric-change positive">+12% this session</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="avgResponseTime">0.8s</div>
                            <div class="metric-label">Avg Response Time</div>
                            <div class="metric-change positive">-15% faster</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="successRate">98%</div>
                            <div class="metric-label">Success Rate</div>
                            <div class="metric-change positive">+2% improved</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="activeAgents">3</div>
                            <div class="metric-label">Active Agents</div>
                            <div class="metric-change">All online</div>
                        </div>
                    </div>
                    <div style="height: 300px; background: var(--dark-surface); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--muted-text);">
                        <i class="fas fa-chart-line" style="font-size: 48px; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>

            <!-- Agents Panel -->
            <div class="panel" id="agentsPanel">
                <div class="panel-header">
                    <div class="panel-title">Agent Management</div>
                    <div class="panel-subtitle">Monitor and configure AI agents</div>
                </div>
                <div class="panel-content">
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                                <div class="category-icon sql"><i class="fas fa-database"></i></div>
                                <div>
                                    <div class="metric-label" style="margin-bottom: 4px;">SQL Agent</div>
                                    <div class="metric-value" style="font-size: 16px;">SQLCoder</div>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: var(--success-color);">
                                <i class="fas fa-circle" style="font-size: 8px;"></i> Online
                            </div>
                        </div>
                        <div class="metric-card">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                                <div class="category-icon general"><i class="fas fa-comment"></i></div>
                                <div>
                                    <div class="metric-label" style="margin-bottom: 4px;">General Agent</div>
                                    <div class="metric-value" style="font-size: 16px;">Qwen</div>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: var(--success-color);">
                                <i class="fas fa-circle" style="font-size: 8px;"></i> Online
                            </div>
                        </div>
                        <div class="metric-card">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                                <div class="category-icon tools"><i class="fas fa-tools"></i></div>
                                <div>
                                    <div class="metric-label" style="margin-bottom: 4px;">Tool Agent</div>
                                    <div class="metric-value" style="font-size: 16px;">Mistral</div>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: var(--success-color);">
                                <i class="fas fa-circle" style="font-size: 8px;"></i> Online
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Panel -->
            <div class="panel" id="databasePanel">
                <div class="panel-header">
                    <div class="panel-title">Database Explorer</div>
                    <div class="panel-subtitle">Browse and query your data</div>
                </div>
                <div class="panel-content">
                    <div style="background: var(--dark-surface); border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                        <h3 style="margin-bottom: 16px; color: var(--light-text);">Database Schema</h3>
                        <div style="font-family: monospace; font-size: 13px; color: var(--muted-text);">
                            <div style="margin-bottom: 12px;">
                                <strong style="color: var(--primary-color);">inventory</strong>
                                <div style="margin-left: 16px;">
                                    • id (INTEGER PRIMARY KEY)<br>
                                    • title (TEXT)<br>
                                    • description (TEXT)<br>
                                    • quantity (INTEGER)<br>
                                    • category_id (INTEGER)
                                </div>
                            </div>
                            <div>
                                <strong style="color: var(--primary-color);">categories</strong>
                                <div style="margin-left: 16px;">
                                    • id (INTEGER PRIMARY KEY)<br>
                                    • title (TEXT)<br>
                                    • description (TEXT)
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value">10</div>
                            <div class="metric-label">Total Items</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">5</div>
                            <div class="metric-label">Categories</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">₦5.45M</div>
                            <div class="metric-label">Total Value</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tools Panel -->
            <div class="panel" id="toolsPanel">
                <div class="panel-header">
                    <div class="panel-title">Available Tools</div>
                    <div class="panel-subtitle">Integrated tools and services</div>
                </div>
                <div class="panel-content">
                    <div class="metrics-grid">
                        <div class="metric-card" style="text-align: left;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                                <i class="fas fa-cloud-sun" style="color: var(--warning-color);"></i>
                                <strong>Weather Checker</strong>
                            </div>
                            <div style="font-size: 12px; color: var(--muted-text);">Check weather conditions for any city</div>
                        </div>
                        <div class="metric-card" style="text-align: left;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                                <i class="fas fa-newspaper" style="color: var(--primary-color);"></i>
                                <strong>News Browser</strong>
                            </div>
                            <div style="font-size: 12px; color: var(--muted-text);">Browse latest news and articles</div>
                        </div>
                        <div class="metric-card" style="text-align: left;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                                <i class="fas fa-sticky-note" style="color: var(--success-color);"></i>
                                <strong>Note Taker</strong>
                            </div>
                            <div style="font-size: 12px; color: var(--muted-text);">Create and manage notes</div>
                        </div>
                        <div class="metric-card" style="text-align: left;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                                <i class="fas fa-calendar" style="color: var(--accent-color);"></i>
                                <strong>Calendar Manager</strong>
                            </div>
                            <div style="font-size: 12px; color: var(--muted-text);">Manage events and schedules</div>
                        </div>
                        <div class="metric-card" style="text-align: left;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                                <i class="fas fa-envelope" style="color: var(--error-color);"></i>
                                <strong>Email Manager</strong>
                            </div>
                            <div style="font-size: 12px; color: var(--muted-text);">Send and manage emails</div>
                        </div>
                        <div class="metric-card" style="text-align: left;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                                <i class="fab fa-whatsapp" style="color: var(--success-color);"></i>
                                <strong>WhatsApp Messenger</strong>
                            </div>
                            <div style="font-size: 12px; color: var(--muted-text);">Send WhatsApp messages</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Memory Panel -->
            <div class="panel" id="memoryPanel">
                <div class="panel-header">
                    <div class="panel-title">Memory System</div>
                    <div class="panel-subtitle">Conversation history and learned information</div>
                </div>
                <div class="panel-content">
                    <div style="text-align: center; padding: 60px 20px; color: var(--muted-text);">
                        <i class="fas fa-brain" style="font-size: 48px; opacity: 0.3; margin-bottom: 16px;"></i>
                        <div>Memory system is active and learning from your conversations</div>
                    </div>
                </div>
            </div>

            <!-- Settings Panel -->
            <div class="panel" id="settingsPanel">
                <div class="panel-header">
                    <div class="panel-title">Settings</div>
                    <div class="panel-subtitle">Configure your AI assistant</div>
                </div>
                <div class="panel-content">
                    <div style="text-align: center; padding: 60px 20px; color: var(--muted-text);">
                        <i class="fas fa-cog" style="font-size: 48px; opacity: 0.3; margin-bottom: 16px;"></i>
                        <div>Settings panel - Coming soon</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Application State
        const AppState = {
            API_BASE: 'http://localhost:3023/v1/playground',
            teamId: null,
            sessionId: null,
            currentPanel: 'chat',
            isConnected: false,
            messageCount: 0,
            sessionStartTime: Date.now(),
            activeAgent: 'General',
            agents: {
                sql: { name: 'SQL Agent', model: 'SQLCoder', status: 'offline' },
                general: { name: 'General Agent', model: 'Qwen', status: 'offline' },
                tools: { name: 'Tool Agent', model: 'Mistral', status: 'offline' }
            }
        };

        // DOM Elements
        const elements = {
            sidebar: document.getElementById('sidebar'),
            sidebarToggle: document.getElementById('sidebarToggle'),
            pageTitle: document.getElementById('pageTitle'),
            currentPanel: document.getElementById('currentPanel'),
            connectionStatus: document.getElementById('connectionStatus'),
            agentStatus: document.getElementById('agentStatus'),
            messages: document.getElementById('messages'),
            typingIndicator: document.getElementById('typingIndicator'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            messageCount: document.getElementById('messageCount'),
            sessionTime: document.getElementById('sessionTime'),
            activeAgent: document.getElementById('activeAgent'),
            quickActionsToggle: document.getElementById('quickActionsToggle'),
            exampleCategories: document.getElementById('exampleCategories'),
            welcomeTime: document.getElementById('welcomeTime')
        };
        
        // Utility Functions
        class Utils {
            static formatTime(timestamp) {
                return new Date(timestamp).toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            static formatDuration(ms) {
                const seconds = Math.floor(ms / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);

                if (hours > 0) {
                    return `${hours.toString().padStart(2, '0')}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
                }
                return `${minutes.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
            }

            static detectAgent(message) {
                const lowerMessage = message.toLowerCase();
                if (lowerMessage.includes('select') || lowerMessage.includes('database') || lowerMessage.includes('inventory') || lowerMessage.includes('categories')) {
                    return 'sql';
                } else if (lowerMessage.includes('weather') || lowerMessage.includes('note') || lowerMessage.includes('email') || lowerMessage.includes('calendar')) {
                    return 'tools';
                }
                return 'general';
            }

            static parseMarkdown(text) {
                if (typeof marked !== 'undefined') {
                    return marked.parse(text);
                }
                // Fallback simple markdown parsing
                return text
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code>$1</code>')
                    .replace(/\n/g, '<br>');
            }
        }

        // Application Controller
        class ShagoApp {
            constructor() {
                this.init();
                this.setupEventListeners();
                this.startSessionTimer();
            }

            async init() {
                try {
                    // Set welcome time
                    if (elements.welcomeTime) {
                        elements.welcomeTime.textContent = Utils.formatTime(Date.now());
                    }

                    // Update connection status
                    this.updateConnectionStatus('connecting', 'Connecting to Shago...');

                    // Get available teams
                    const response = await fetch(`${AppState.API_BASE}/teams`);
                    const teams = await response.json();

                    if (teams && teams.length > 0) {
                        AppState.teamId = teams[0].team_id;
                        AppState.isConnected = true;

                        // Update agent statuses
                        if (teams[0].members) {
                            teams[0].members.forEach(member => {
                                const agentKey = member.name.toLowerCase().includes('sql') ? 'sql' :
                                               member.name.toLowerCase().includes('tool') ? 'tools' : 'general';
                                AppState.agents[agentKey].status = 'online';
                            });
                        }

                        this.updateConnectionStatus('connected', `Connected to ${teams[0].name}`);
                        this.updateAgentStatus();

                        console.log('✅ Connected to Shago AI Assistant');
                    } else {
                        throw new Error('No teams found');
                    }
                } catch (error) {
                    AppState.isConnected = false;
                    this.updateConnectionStatus('error', 'Failed to connect to server');
                    console.error('Connection error:', error);
                }
            }
        
            setupEventListeners() {
                // Sidebar toggle
                elements.sidebarToggle?.addEventListener('click', () => {
                    elements.sidebar?.classList.toggle('collapsed');
                });

                // Navigation
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const panel = item.dataset.panel;
                        if (panel) {
                            this.switchPanel(panel);
                        }
                    });
                });

                // Message input
                elements.messageInput?.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                elements.messageInput?.addEventListener('input', () => {
                    this.autoResizeTextarea();
                });

                // Send button
                elements.sendButton?.addEventListener('click', () => {
                    this.sendMessage();
                });

                // Quick actions toggle
                elements.quickActionsToggle?.addEventListener('click', () => {
                    elements.exampleCategories?.classList.toggle('hidden');
                    const icon = elements.quickActionsToggle.querySelector('i');
                    icon.classList.toggle('fa-chevron-up');
                    icon.classList.toggle('fa-chevron-down');
                });

                // Mobile sidebar
                if (window.innerWidth <= 768) {
                    document.addEventListener('click', (e) => {
                        if (!elements.sidebar?.contains(e.target) && !e.target.closest('.sidebar-toggle')) {
                            elements.sidebar?.classList.remove('open');
                        }
                    });
                }
            }

            updateConnectionStatus(type, message) {
                if (!elements.connectionStatus) return;

                elements.connectionStatus.className = `status-indicator ${type}`;
                elements.connectionStatus.innerHTML = `
                    <div class="status-pulse"></div>
                    <span>${message}</span>
                `;
            }

            updateAgentStatus() {
                if (!elements.agentStatus) return;

                Object.keys(AppState.agents).forEach(key => {
                    const chip = elements.agentStatus.querySelector(`[data-agent="${key}"]`);
                    if (chip) {
                        chip.classList.toggle('active', AppState.agents[key].status === 'online');
                    }
                });
            }

            switchPanel(panelName) {
                // Update navigation
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.toggle('active', item.dataset.panel === panelName);
                });

                // Update panels
                document.querySelectorAll('.panel').forEach(panel => {
                    panel.classList.toggle('active', panel.id === `${panelName}Panel`);
                });

                // Update header
                AppState.currentPanel = panelName;
                const titles = {
                    chat: 'Chat Interface',
                    analytics: 'Analytics Dashboard',
                    agents: 'Agent Management',
                    database: 'Database Explorer',
                    tools: 'Available Tools',
                    memory: 'Memory System',
                    settings: 'Settings'
                };

                if (elements.pageTitle) {
                    elements.pageTitle.textContent = titles[panelName] || 'Shago AI';
                }
                if (elements.currentPanel) {
                    elements.currentPanel.textContent = panelName.charAt(0).toUpperCase() + panelName.slice(1);
                }
            }

            autoResizeTextarea() {
                if (!elements.messageInput) return;

                elements.messageInput.style.height = 'auto';
                elements.messageInput.style.height = Math.min(elements.messageInput.scrollHeight, 120) + 'px';
            }

            startSessionTimer() {
                setInterval(() => {
                    const elapsed = Date.now() - AppState.sessionStartTime;
                    if (elements.sessionTime) {
                        elements.sessionTime.textContent = Utils.formatDuration(elapsed);
                    }
                }, 1000);
            }

            updateStats() {
                if (elements.messageCount) {
                    elements.messageCount.textContent = AppState.messageCount;
                }
                if (elements.activeAgent) {
                    elements.activeAgent.textContent = AppState.activeAgent;
                }
            }
        
            addMessage(type, content, sender = null) {
                if (!elements.messages) return;

                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type} fade-in`;

                // Avatar
                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';

                const avatarIcons = {
                    user: 'fas fa-user',
                    assistant: 'fas fa-robot',
                    system: 'fas fa-info-circle'
                };
                avatar.innerHTML = `<i class="${avatarIcons[type] || 'fas fa-circle'}"></i>`;

                // Message wrapper
                const wrapper = document.createElement('div');
                wrapper.className = 'message-wrapper';

                // Header
                const header = document.createElement('div');
                header.className = 'message-header';

                const senderSpan = document.createElement('span');
                senderSpan.className = 'message-sender';
                senderSpan.textContent = sender || (type === 'user' ? 'You' : type === 'assistant' ? 'Shago' : 'System');

                const timeSpan = document.createElement('span');
                timeSpan.className = 'message-time';
                timeSpan.textContent = Utils.formatTime(Date.now());

                header.appendChild(senderSpan);
                header.appendChild(timeSpan);

                // Content
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.innerHTML = Utils.parseMarkdown(content);

                // Actions
                const actions = document.createElement('div');
                actions.className = 'message-actions';
                actions.innerHTML = `
                    <button class="action-btn" onclick="app.copyMessage(this)" data-tooltip="Copy">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="action-btn" onclick="app.regenerateResponse(this)" data-tooltip="Regenerate">
                        <i class="fas fa-redo"></i>
                    </button>
                `;

                wrapper.appendChild(header);
                wrapper.appendChild(contentDiv);
                if (type === 'assistant') {
                    wrapper.appendChild(actions);
                }

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(wrapper);

                elements.messages.appendChild(messageDiv);
                this.scrollToBottom();

                // Update message count
                AppState.messageCount++;
                this.updateStats();

                // Syntax highlighting
                if (typeof hljs !== 'undefined') {
                    contentDiv.querySelectorAll('pre code').forEach(block => {
                        hljs.highlightElement(block);
                    });
                }
            }

            showTypingIndicator() {
                if (elements.typingIndicator) {
                    elements.typingIndicator.style.display = 'block';
                    this.scrollToBottom();
                }
            }

            hideTypingIndicator() {
                if (elements.typingIndicator) {
                    elements.typingIndicator.style.display = 'none';
                }
            }

            scrollToBottom() {
                if (elements.messages) {
                    elements.messages.scrollTop = elements.messages.scrollHeight;
                }
            }

            setLoading(loading) {
                if (!elements.sendButton) return;

                if (loading) {
                    elements.sendButton.disabled = true;
                    elements.sendButton.classList.add('loading');
                    elements.sendButton.innerHTML = `
                        <div class="spinner"></div>
                        <span>Sending...</span>
                    `;
                } else {
                    elements.sendButton.disabled = false;
                    elements.sendButton.classList.remove('loading');
                    elements.sendButton.innerHTML = `
                        <span class="send-text">Send</span>
                        <i class="fas fa-paper-plane"></i>
                    `;
                }
            }
        
            async sendMessage(message = null) {
                if (!AppState.isConnected || !AppState.teamId) {
                    this.addMessage('system', 'Error: Not connected to server');
                    return;
                }

                const messageText = message || elements.messageInput?.value?.trim();
                if (!messageText) return;

                // Clear input
                if (elements.messageInput && !message) {
                    elements.messageInput.value = '';
                    this.autoResizeTextarea();
                }

                // Detect and update active agent
                const detectedAgent = Utils.detectAgent(messageText);
                AppState.activeAgent = AppState.agents[detectedAgent].name;
                this.updateStats();

                // Add user message
                this.addMessage('user', messageText);

                // Show loading states
                this.setLoading(true);
                this.showTypingIndicator();

                try {
                    // Create form data
                    const formData = new FormData();
                    formData.append('message', messageText);
                    formData.append('stream', 'false');
                    if (AppState.sessionId) {
                        formData.append('session_id', AppState.sessionId);
                    }

                    // Send to team
                    const response = await fetch(`${AppState.API_BASE}/teams/${AppState.teamId}/runs`, {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();

                    // Extract session ID for future messages
                    if (result.session_id) {
                        AppState.sessionId = result.session_id;
                    }

                    // Add assistant response
                    let responseContent = '';
                    if (result.content) {
                        responseContent = result.content;
                    } else if (result.messages && result.messages.length > 0) {
                        const lastMessage = result.messages[result.messages.length - 1];
                        responseContent = lastMessage.content || 'No response content available';
                    } else {
                        responseContent = 'Response received but no content found';
                    }

                    this.addMessage('assistant', responseContent, 'Shago');

                } catch (error) {
                    this.addMessage('system', `Error: ${error.message}`);
                    console.error('Send error:', error);
                } finally {
                    this.setLoading(false);
                    this.hideTypingIndicator();
                    elements.messageInput?.focus();
                }
            }

            copyMessage(button) {
                const messageContent = button.closest('.message-wrapper').querySelector('.message-content');
                const text = messageContent.textContent;

                navigator.clipboard.writeText(text).then(() => {
                    // Show feedback
                    const originalIcon = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        button.innerHTML = originalIcon;
                    }, 1000);
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                });
            }

            regenerateResponse(button) {
                // Find the user message before this assistant message
                const assistantMessage = button.closest('.message');
                const messages = Array.from(elements.messages.children);
                const messageIndex = messages.indexOf(assistantMessage);

                if (messageIndex > 0) {
                    const userMessage = messages[messageIndex - 1];
                    if (userMessage.classList.contains('user')) {
                        const userContent = userMessage.querySelector('.message-content').textContent;

                        // Remove the assistant message
                        assistantMessage.remove();
                        AppState.messageCount--;

                        // Resend the user message
                        this.sendMessage(userContent);
                    }
                }
            }
        }
        
        // Global helper functions for onclick handlers
        function sendExample(message) {
            if (window.app) {
                window.app.sendMessage(message);
            }
        }

        // Initialize application
        document.addEventListener('DOMContentLoaded', () => {
            window.app = new ShagoApp();

            // Add some demo data to analytics
            setTimeout(() => {
                if (document.getElementById('totalMessages')) {
                    document.getElementById('totalMessages').textContent = AppState.messageCount;
                }
            }, 1000);
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                elements.sidebar?.classList.add('collapsed');
            } else {
                elements.sidebar?.classList.remove('collapsed');
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                elements.messageInput?.focus();
            }

            // Escape to close sidebar on mobile
            if (e.key === 'Escape' && window.innerWidth <= 768) {
                elements.sidebar?.classList.remove('open');
            }
        });

        // Service Worker for offline functionality (optional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                // Register service worker for offline functionality
                // navigator.serviceWorker.register('/sw.js');
            });
        }

        // Performance monitoring
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`🚀 Shago AI Playground loaded in ${Math.round(loadTime)}ms`);
        });
    </script>
</body>
</html>
