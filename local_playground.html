<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shago AI Assistant - Local Playground</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .status {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #007bff;
            color: white;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e9ecef;
            color: #333;
        }
        
        .message.system .message-content {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            font-style: italic;
        }
        
        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .input-form {
            display: flex;
            gap: 10px;
        }
        
        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .input-field:focus {
            border-color: #007bff;
        }
        
        .send-button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .send-button:hover {
            background: #0056b3;
        }
        
        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #6c757d;
        }
        
        .examples {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .examples h4 {
            margin-bottom: 8px;
            color: #495057;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .example-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .example-btn {
            padding: 4px 8px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            font-size: 11px;
            cursor: pointer;
            color: #495057;
        }
        
        .example-btn:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Shago AI Assistant</h1>
            <p>Local Offline Multi-Agent AI Assistant</p>
        </div>
        
        <div class="status" id="status">
            🔄 Connecting to local server...
        </div>
        
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message system">
                    <div class="message-content">
                        Welcome to Shago! I'm your offline AI assistant with specialized agents for different tasks:
                        <br>• <strong>SQL Agent</strong>: Database queries
                        <br>• <strong>General Agent</strong>: Conversations  
                        <br>• <strong>Tool Agent</strong>: Various operations
                        <br><br>Try asking me something!
                    </div>
                </div>
            </div>
            
            <div class="loading" id="loading">
                🤔 Shago is thinking...
            </div>
        </div>
        
        <div class="examples">
            <h4>Quick Examples</h4>
            <div class="example-buttons">
                <button class="example-btn" onclick="sendExample('Show me all inventory items')">📦 Show inventory</button>
                <button class="example-btn" onclick="sendExample('What categories do we have?')">📋 Show categories</button>
                <button class="example-btn" onclick="sendExample('Hello, how are you?')">👋 Say hello</button>
                <button class="example-btn" onclick="sendExample('Check weather in Lagos')">🌤️ Check weather</button>
                <button class="example-btn" onclick="sendExample('Take a note: Meeting at 3pm')">📝 Take note</button>
            </div>
        </div>
        
        <div class="input-container">
            <form class="input-form" id="chatForm">
                <input 
                    type="text" 
                    class="input-field" 
                    id="messageInput" 
                    placeholder="Type your message to Shago..."
                    autocomplete="off"
                >
                <button type="submit" class="send-button" id="sendButton">Send</button>
            </form>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3023/v1/playground';
        let teamId = null;
        let sessionId = null;
        
        // DOM elements
        const statusEl = document.getElementById('status');
        const messagesEl = document.getElementById('messages');
        const loadingEl = document.getElementById('loading');
        const chatForm = document.getElementById('chatForm');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        
        // Initialize
        async function init() {
            try {
                // Get available teams
                const response = await fetch(`${API_BASE}/teams`);
                const teams = await response.json();
                
                if (teams && teams.length > 0) {
                    teamId = teams[0].team_id;
                    updateStatus('connected', `✅ Connected to Shago (${teams[0].name})`);
                } else {
                    updateStatus('error', '❌ No teams found');
                }
            } catch (error) {
                updateStatus('error', '❌ Failed to connect to local server');
                console.error('Connection error:', error);
            }
        }
        
        function updateStatus(type, message) {
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }
        
        function addMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = content.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(contentDiv);
            messagesEl.appendChild(messageDiv);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }
        
        async function sendMessage(message) {
            if (!teamId) {
                addMessage('system', 'Error: Not connected to server');
                return;
            }
            
            // Add user message
            addMessage('user', message);
            
            // Show loading
            loadingEl.style.display = 'block';
            sendButton.disabled = true;
            
            try {
                // Create form data
                const formData = new FormData();
                formData.append('message', message);
                formData.append('stream', 'false');
                if (sessionId) {
                    formData.append('session_id', sessionId);
                }
                
                // Send to team
                const response = await fetch(`${API_BASE}/teams/${teamId}/runs`, {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                
                // Extract session ID for future messages
                if (result.session_id) {
                    sessionId = result.session_id;
                }
                
                // Add assistant response
                if (result.content) {
                    addMessage('assistant', result.content);
                } else if (result.messages && result.messages.length > 0) {
                    const lastMessage = result.messages[result.messages.length - 1];
                    addMessage('assistant', lastMessage.content || 'No response content');
                } else {
                    addMessage('assistant', 'Response received but no content found');
                }
                
            } catch (error) {
                addMessage('system', `Error: ${error.message}`);
                console.error('Send error:', error);
            } finally {
                loadingEl.style.display = 'none';
                sendButton.disabled = false;
                messageInput.focus();
            }
        }
        
        function sendExample(message) {
            messageInput.value = message;
            sendMessage(message);
            messageInput.value = '';
        }
        
        // Event listeners
        chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const message = messageInput.value.trim();
            if (message) {
                sendMessage(message);
                messageInput.value = '';
            }
        });
        
        // Initialize on load
        init();
    </script>
</body>
</html>
