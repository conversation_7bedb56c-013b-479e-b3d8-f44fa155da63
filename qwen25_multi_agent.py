#!/usr/bin/env python3
"""
Multi-Agent System using qwen2.5:7b with custom function calling
Based on successful testing of qwen2.5:7b capabilities
"""

import requests
import json
import sqlite3
from typing import Dict, Any, List
from config import OLLAMA_BASE_URL, MODELS, DATABASE_FILE

class QwenMultiAgentSystem:
    def __init__(self):
        self.models = MODELS
        self.db_path = DATABASE_FILE
        
    def route_request(self, user_input: str) -> Dict[str, Any]:
        """Smart routing using qwen2.5:7b with <no-think> flag"""
        
        routing_prompt = f"""<no-think>
You are a routing assistant. Analyze user requests and respond with one of these actions:
- ROUTE: sql_query (for database questions like "show customers", "count orders", "list inventory")
- ROUTE: function_call (for weather, web search, email, external APIs)
- ROUTE: general_chat (for greetings, explanations, general conversation)

Only respond with the ROUTE: action_name format.

User request: "{user_input}"
</no-think>"""
        
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": self.models["coordinator"],  # qwen2.5:7b
                    "messages": [{"role": "user", "content": routing_prompt}],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                route_response = result["message"]["content"].strip().lower()
                
                print(f"🚦 Routing decision: {route_response}")
                
                if "sql_query" in route_response:
                    return self.handle_sql_request(user_input)
                elif "function_call" in route_response:
                    return self.handle_function_request(user_input)
                else:
                    return self.handle_general_chat(user_input)
            else:
                return {"error": f"Routing failed: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Routing error: {str(e)}"}
    
    def handle_sql_request(self, user_input: str) -> Dict[str, Any]:
        """Handle database queries using SQL specialist"""
        
        # Get database schema for context
        schema_info = self.get_database_schema()
        
        sql_prompt = f"""You are a SQL expert. Generate a SQL query for this request.

Database Schema:
{schema_info}

User Request: "{user_input}"

Generate a valid SQL SELECT query. Examples:
- For "show customers": SELECT * FROM customers;
- For "count orders": SELECT COUNT(*) FROM orders;
- For "list inventory": SELECT * FROM inventory;

Respond with only the SQL query, no explanations or markdown."""
        
        try:
            # Use qwen2.5:7b for SQL generation (it works better than sqlcoder)
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": self.models["coordinator"],  # qwen2.5:7b
                    "messages": [{"role": "user", "content": sql_prompt}],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                sql_query = result["message"]["content"].strip()
                
                # Clean up the SQL query
                sql_query = self.clean_sql_query(sql_query)
                
                print(f"🗃️ Generated SQL: {sql_query}")
                
                # Execute the query
                query_result = self.execute_sql_query(sql_query)
                
                if query_result["success"]:
                    # Format the results using qwen2.5:7b
                    return self.format_sql_results(user_input, sql_query, query_result["data"])
                else:
                    return {"error": f"SQL execution failed: {query_result['error']}"}
            else:
                return {"error": f"SQL generation failed: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"SQL handling error: {str(e)}"}
    
    def handle_function_request(self, user_input: str) -> Dict[str, Any]:
        """Handle function calls using qwen2.5:7b with custom format"""
        
        function_prompt = f"""<no-think>
You are a function calling assistant. When users need external services, respond with function calls.

Available functions:
- get_weather(location: str, unit: str = "celsius") -> str
- search_web(query: str) -> str
- send_email(to: str, subject: str, body: str) -> str

When you need to call a function, respond with:
FUNCTION_CALL: function_name(parameters)

Example: FUNCTION_CALL: get_weather(location="San Francisco", unit="celsius")

User request: "{user_input}"
</no-think>"""
        
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": self.models["coordinator"],  # qwen2.5:7b
                    "messages": [{"role": "user", "content": function_prompt}],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["message"]["content"].strip()
                
                print(f"🔧 Function response: {content}")
                
                if "FUNCTION_CALL:" in content:
                    # Parse and simulate function execution
                    return self.execute_function_call(content, user_input)
                else:
                    return {
                        "type": "function_response",
                        "content": content,
                        "success": True
                    }
            else:
                return {"error": f"Function calling failed: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Function handling error: {str(e)}"}
    
    def handle_general_chat(self, user_input: str) -> Dict[str, Any]:
        """Handle general conversation using qwen2.5:7b"""
        
        chat_prompt = f"""<no-think>
You are a helpful assistant. Respond naturally and helpfully to the user's message.
</no-think>

User: {user_input}"""
        
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": self.models["coordinator"],  # qwen2.5:7b
                    "messages": [{"role": "user", "content": chat_prompt}],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["message"]["content"].strip()
                
                return {
                    "type": "general_chat",
                    "content": content,
                    "success": True
                }
            else:
                return {"error": f"Chat failed: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Chat error: {str(e)}"}
    
    def get_database_schema(self) -> str:
        """Get database schema information"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            schema_info = "Database Tables:\n"
            for table in tables:
                table_name = table[0]
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                schema_info += f"\n{table_name}:\n"
                for col in columns:
                    schema_info += f"  - {col[1]} ({col[2]})\n"
            
            conn.close()
            return schema_info
            
        except Exception as e:
            return f"Schema error: {str(e)}"
    
    def clean_sql_query(self, sql_query: str) -> str:
        """Clean and validate SQL query"""
        # Remove markdown formatting
        sql_query = sql_query.replace("```sql", "").replace("```", "")
        
        # Remove extra whitespace
        sql_query = sql_query.strip()
        
        # Ensure it ends with semicolon
        if not sql_query.endswith(';'):
            sql_query += ';'
            
        return sql_query
    
    def execute_sql_query(self, sql_query: str) -> Dict[str, Any]:
        """Execute SQL query safely"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(sql_query)
            
            if sql_query.strip().upper().startswith('SELECT'):
                data = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                
                # Convert to list of dictionaries
                result_data = []
                for row in data:
                    result_data.append(dict(zip(columns, row)))
                
                conn.close()
                return {"success": True, "data": result_data}
            else:
                conn.commit()
                conn.close()
                return {"success": True, "data": "Query executed successfully"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def format_sql_results(self, user_input: str, sql_query: str, data: List[Dict]) -> Dict[str, Any]:
        """Format SQL results using qwen2.5:7b"""
        
        format_prompt = f"""<no-think>
Format these database results for the user in a clear, readable way.

User Question: "{user_input}"
SQL Query: {sql_query}
Results: {json.dumps(data, indent=2)}

Provide a natural language summary of the results.
</no-think>"""
        
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": self.models["coordinator"],
                    "messages": [{"role": "user", "content": format_prompt}],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                formatted_content = result["message"]["content"].strip()
                
                return {
                    "type": "sql_result",
                    "content": formatted_content,
                    "sql_query": sql_query,
                    "raw_data": data,
                    "success": True
                }
            else:
                return {
                    "type": "sql_result",
                    "content": f"Found {len(data)} results",
                    "sql_query": sql_query,
                    "raw_data": data,
                    "success": True
                }
                
        except Exception as e:
            return {
                "type": "sql_result",
                "content": f"Found {len(data)} results (formatting error: {str(e)})",
                "sql_query": sql_query,
                "raw_data": data,
                "success": True
            }
    
    def execute_function_call(self, function_call: str, user_input: str) -> Dict[str, Any]:
        """Simulate function execution"""
        
        # Extract function name and parameters
        if "get_weather" in function_call:
            return {
                "type": "function_result",
                "content": "🌤️ Weather simulation: 22°C, partly cloudy in the requested location.",
                "function_called": "get_weather",
                "success": True
            }
        elif "search_web" in function_call:
            return {
                "type": "function_result", 
                "content": "🔍 Web search simulation: Found relevant information about your query.",
                "function_called": "search_web",
                "success": True
            }
        else:
            return {
                "type": "function_result",
                "content": f"Function call detected: {function_call}",
                "function_called": "unknown",
                "success": True
            }

def main():
    """Test the qwen2.5 multi-agent system"""
    
    print("🚀 Starting Qwen2.5 Multi-Agent System")
    print("=" * 50)
    
    system = QwenMultiAgentSystem()
    
    test_queries = [
        "Show me all customers in the database",
        "What's the weather like in Tokyo?", 
        "Hello, how are you today?",
        "Count how many orders we have",
        "Search the web for Python tutorials"
    ]
    
    for query in test_queries:
        print(f"\n❓ Query: {query}")
        print("-" * 30)
        
        result = system.route_request(query)
        
        if result.get("success"):
            print(f"✅ Type: {result.get('type', 'unknown')}")
            print(f"💬 Response: {result.get('content', 'No content')}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
