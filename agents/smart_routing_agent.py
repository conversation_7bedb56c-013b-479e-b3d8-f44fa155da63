from agno.agent import Agent
from agno.models.ollama import Ollama
from config import MODELS, DATABASE_FILE
import re
import sqlite3

class SmartRoutingAgent(Agent):
    """
    A smart agent that explicitly routes database queries to SQL tools.
    This works around the issue where models don't properly call tools.
    """
    
    def __init__(self):
        super().__init__(
            name="Smart_Routing_Agent",
            model=Ollama(id=MODELS["coordinator"]),  # Use qwen - works well for our smart routing
            instructions=[
                "You are <PERSON><PERSON><PERSON>, an intelligent AI assistant.",
                "You have access to a database with inventory and categories.",
                "When you receive database query results, format them nicely for the user."
            ]
        )
    
    def run(self, message: str, **kwargs):
        """
        Override the run method to add smart routing logic.
        """
        # Check if this is a database-related query
        if self._is_database_query(message):
            return self._handle_database_query(message)
        else:
            # For non-database queries, use the normal agent behavior
            return super().run(message, **kwargs)
    
    def _is_database_query(self, message: str) -> bool:
        """
        Check if the message is asking for database information.
        """
        database_keywords = [
            'categories', 'category', 'inventory', 'stock', 'items', 'products',
            'what do we have', 'show me', 'list', 'database', 'table'
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in database_keywords)
    
    def _execute_sql_query(self, sql_query: str) -> str:
        """Execute a SQL query directly on the database."""
        try:
            # Basic security check - only allow SELECT statements
            query_upper = sql_query.strip().upper()
            if not query_upper.startswith('SELECT'):
                return "Error: Only SELECT queries are allowed for security reasons."

            con = sqlite3.connect(DATABASE_FILE)
            cur = con.cursor()
            res = cur.execute(sql_query)
            results = res.fetchall()
            return str(results)
        except Exception as e:
            return f"Error executing query: {e}"
        finally:
            if 'con' in locals():
                con.close()

    def _handle_database_query(self, message: str):
        """
        Handle database queries by determining the appropriate SQL and executing it.
        """
        message_lower = message.lower()

        try:
            # Determine the appropriate SQL query based on the message
            if 'categories' in message_lower or 'category' in message_lower:
                sql_query = "SELECT * FROM categories"
                result = self._execute_sql_query(sql_query)
                return self._format_categories_response(result)

            elif 'inventory' in message_lower or 'items' in message_lower or 'products' in message_lower:
                sql_query = "SELECT i.*, c.title as category_name FROM inventory i LEFT JOIN categories c ON i.category_id = c.id"
                result = self._execute_sql_query(sql_query)
                return self._format_inventory_response(result)

            elif 'stock' in message_lower:
                sql_query = "SELECT i.title, i.quantity, c.title as category FROM inventory i LEFT JOIN categories c ON i.category_id = c.id WHERE i.quantity > 0"
                result = self._execute_sql_query(sql_query)
                return self._format_stock_response(result)

            else:
                # Generic database query
                sql_query = "SELECT * FROM categories"
                result = self._execute_sql_query(sql_query)
                return self._format_categories_response(result)

        except Exception as e:
            return self._create_response(f"I encountered an error while querying the database: {str(e)}")
    
    def _format_categories_response(self, sql_result: str):
        """Format the categories SQL result into a nice response."""
        try:
            # Parse the SQL result (it's a string representation of a list of tuples)
            import ast
            categories = ast.literal_eval(sql_result)
            
            if not categories:
                return self._create_response("No categories found in the database.")
            
            response = "Here are the categories we have in stock:\n\n"
            for cat in categories:
                cat_id, title, description = cat
                response += f"• **{title}** - {description}\n"
            
            return self._create_response(response)
            
        except Exception as e:
            return self._create_response(f"Found categories data but had trouble formatting it: {sql_result}")
    
    def _format_inventory_response(self, sql_result: str):
        """Format the inventory SQL result into a nice response."""
        try:
            import ast
            items = ast.literal_eval(sql_result)
            
            if not items:
                return self._create_response("No items found in the inventory.")
            
            response = "Here are the items in our inventory:\n\n"
            for item in items:
                item_id, title, description, quantity, category_id, category_name = item
                response += f"• **{title}** ({category_name or 'No Category'})\n"
                response += f"  - Quantity: {quantity}\n"
                response += f"  - Description: {description}\n\n"
            
            return self._create_response(response)
            
        except Exception as e:
            return self._create_response(f"Found inventory data but had trouble formatting it: {sql_result}")
    
    def _format_stock_response(self, sql_result: str):
        """Format the stock SQL result into a nice response."""
        try:
            import ast
            items = ast.literal_eval(sql_result)
            
            if not items:
                return self._create_response("No items currently in stock.")
            
            response = "Here are the items currently in stock:\n\n"
            for item in items:
                title, quantity, category = item
                response += f"• **{title}** - {quantity} units ({category})\n"
            
            return self._create_response(response)
            
        except Exception as e:
            return self._create_response(f"Found stock data but had trouble formatting it: {sql_result}")
    
    def _create_response(self, content: str):
        """Create a response object similar to what Agent.run() returns."""
        class Response:
            def __init__(self, content):
                self.content = content
        
        return Response(content)

# Create the smart routing agent instance
smart_routing_agent = SmartRoutingAgent()
