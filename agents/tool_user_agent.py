from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.custom_tools import (
    news_browser,
    weather_checker,
    whatsapp_messenger,
    print_executor,
    inventory_manager,
    note_taker,
    calendar_manager,
    email_manager,
)
from config import MODELS, OLLAMA_BASE_URL, AGENT_INSTRUCTIONS

tool_user_agent = Agent(
    name="Tool_User_Agent",
    model=Ollama(id=MODELS["tool_specialist"], base_url=OLLAMA_BASE_URL), # Specialized for tool use
    tools=[
        news_browser,
        weather_checker,
        whatsapp_messenger,
        print_executor,
        inventory_manager,
        note_taker,
        calendar_manager,
        email_manager,
    ],
    instructions=AGENT_INSTRUCTIONS["tool_user_agent"]
)
