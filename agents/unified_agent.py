from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.custom_tools import (
    read_only_sql_executor, news_browser, weather_checker, 
    whatsapp_messenger, print_executor, inventory_manager,
    note_taker, calendar_manager, email_manager
)
from config import MODELS, OLLAMA_BASE_URL

# Create a unified agent with all tools
unified_agent = Agent(
    name="Shago_Unified_Agent",
    model=Ollama(id=MODELS["coordinator"]),
    tools=[
        read_only_sql_executor,
        news_browser,
        weather_checker,
        whatsapp_messenger,
        print_executor,
        inventory_manager,
        note_taker,
        calendar_manager,
        email_manager
    ],
    instructions=[
        "You are '<PERSON><PERSON><PERSON>', a highly intelligent AI assistant with access to multiple tools.",
        "You have access to a database with inventory and categories information.",
        "",
        "CRITICAL: You MUST use tools to answer questions - do not just describe what you would do!",
        "",
        "TOOL USAGE RULES:",
        "",
        "1. For DATABASE questions (inventory, categories, stock, items):",
        "   - IMMEDIATELY use the 'read_only_sql_executor' tool with appropriate SQL",
        "   - Available tables: inventory (id, title, description, quantity, category_id), categories (id, title, description)",
        "   - Example: User asks 'What categories do we have?' → CALL read_only_sql_executor('SELECT * FROM categories')",
        "   - Do NOT explain what you would do - just DO it by calling the tool",
        "",
        "2. For WEATHER questions: CALL weather_checker tool",
        "3. For NEWS questions: CALL news_browser tool",
        "4. For NOTES: CALL note_taker tool",
        "",
        "5. For GENERAL conversation (greetings, AI questions):",
        "   - Answer directly without using tools",
        "",
        "REMEMBER: When user asks about database content, IMMEDIATELY call the read_only_sql_executor tool.",
        "Do not explain, describe, or suggest - just execute the SQL query using the tool!"
    ]
)
