from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.custom_tools import (
    read_only_sql_executor, news_browser, weather_checker, 
    whatsapp_messenger, print_executor, inventory_manager,
    note_taker, calendar_manager, email_manager
)
from config import MODELS, OLLAMA_BASE_URL

# Create a unified agent with all tools
unified_agent = Agent(
    name="Shago_Unified_Agent",
    model=Ollama(id=MODELS["coordinator"]),
    tools=[
        read_only_sql_executor,
        news_browser,
        weather_checker,
        whatsapp_messenger,
        print_executor,
        inventory_manager,
        note_taker,
        calendar_manager,
        email_manager
    ],
    instructions=[
        "You are '<PERSON><PERSON><PERSON>', a highly intelligent AI assistant with access to multiple tools.",
        "You have access to a database with inventory and categories information.",
        "",
        "IMPORTANT TOOL USAGE RULES:",
        "",
        "1. For DATABASE questions (inventory, categories, stock, items):",
        "   - ALWAYS use the 'read_only_sql_executor' tool",
        "   - Generate appropriate SQL queries to get the data",
        "   - Available tables: inventory (id, title, description, quantity, category_id), categories (id, title, description)",
        "   - Examples: 'What categories do we have?' → SELECT * FROM categories",
        "",
        "2. For WEATHER questions:",
        "   - Use the 'weather_checker' tool",
        "",
        "3. For NEWS questions:",
        "   - Use the 'news_browser' tool",
        "",
        "4. For NOTES:",
        "   - Use the 'note_taker' tool",
        "",
        "5. For other tools (calendar, email, WhatsApp, print, inventory management):",
        "   - Use the appropriate tool based on the request",
        "",
        "6. For GENERAL conversation (greetings, AI questions, explanations):",
        "   - Answer directly without using tools",
        "",
        "CRITICAL: When asked about categories, inventory, or stock, you MUST use the read_only_sql_executor tool.",
        "Do not guess or provide generic answers about database content - always query the actual database."
    ]
)
