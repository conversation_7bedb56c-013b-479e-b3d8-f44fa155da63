#!/usr/bin/env python3
"""
Test the <no-think> flag with current qwen model to see if it improves tool calling.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.custom_tools import read_only_sql_executor
from config import MODELS

def test_no_think_flag():
    """Test qwen with and without <no-think> flag."""
    print("🔍 Testing <no-think> Flag Effect on Tool Calling")
    print("=" * 60)
    
    # Test 1: qwen WITHOUT <no-think> flag
    print("1. Testing qwen WITHOUT <no-think> flag:")
    try:
        agent_without_no_think = Agent(
            name="Qwen_Without_NoThink",
            model=Ollama(id=MODELS["coordinator"]),  # qwen
            tools=[read_only_sql_executor],
            instructions=[
                "You are a helpful AI assistant with database access.",
                "When asked about database content, CALL the read_only_sql_executor tool.",
                "For categories: call read_only_sql_executor('SELECT * FROM categories')",
                "DO NOT explain what you would do - actually call the function!"
            ]
        )
        
        response = agent_without_no_think.run("What categories do we have in stock?")
        print(f"Response: {response.content}")
        
        if any(word in response.content.lower() for word in ["electronics", "clothing", "books"]):
            print("✅ SUCCESS: qwen WITHOUT <no-think> executed SQL and returned data!")
        else:
            print("❌ FAILED: qwen WITHOUT <no-think> explained instead of calling function")
            
    except Exception as e:
        print(f"❌ Error with qwen WITHOUT <no-think>: {e}")
    
    print("\n" + "=" * 60)
    
    # Test 2: qwen WITH <no-think> flag
    print("2. Testing qwen WITH <no-think> flag:")
    try:
        agent_with_no_think = Agent(
            name="Qwen_With_NoThink",
            model=Ollama(id=MODELS["coordinator"]),  # qwen
            tools=[read_only_sql_executor],
            instructions=[
                "<no-think>",  # Add the no-think flag
                "You are a helpful AI assistant with database access.",
                "When asked about database content, CALL the read_only_sql_executor tool.",
                "For categories: call read_only_sql_executor('SELECT * FROM categories')",
                "DO NOT explain what you would do - actually call the function!"
            ]
        )
        
        response = agent_with_no_think.run("What categories do we have in stock?")
        print(f"Response: {response.content}")
        
        if any(word in response.content.lower() for word in ["electronics", "clothing", "books"]):
            print("🎉 SUCCESS: qwen WITH <no-think> executed SQL and returned data!")
        else:
            print("⚠️  PARTIAL: qwen WITH <no-think> still explained instead of calling function")
            
    except Exception as e:
        print(f"❌ Error with qwen WITH <no-think>: {e}")
    
    print("\n" + "=" * 60)
    print("3. Summary:")
    print("This test helps determine if the <no-think> flag improves tool calling behavior.")
    print("If both fail, the issue is deeper than think mode - it's model capability.")
    print("If <no-think> helps, we should use it in all our agents.")

if __name__ == "__main__":
    test_no_think_flag()
