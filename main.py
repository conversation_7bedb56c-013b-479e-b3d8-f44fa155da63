from agno.agent import Agent
from agno.team import Team
from agno.models.ollama import Ollama
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from agno.knowledge.text import TextKnowledgeBase
from agno.vectordb.pgvector import PgVector
from agno.playground import Playground
from fastapi.middleware.cors import CORSMiddleware
import logging
import os

from agents.sql_agent import sql_agent
from agents.general_agent import general_agent
from agents.tool_user_agent import tool_user_agent
from agents.unified_agent import unified_agent
from agents.smart_routing_agent import smart_routing_agent
from tools.custom_tools import read_only_sql_executor
from datetime import datetime
from config import *

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format=LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG["file"]),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- 1. Database and Storage Setup ---
# For a truly offline solution, we'll use SQLite.
# For production, you'd switch to the PostgreSQL connection string.
agent_storage = SqliteStorage(table_name="agent_sessions", db_file=DATABASE_FILE)
memory_db = SqliteMemoryDb(table_name="shago_memory", db_file=DATABASE_FILE)

logger.info(f"Using database file: {DATABASE_FILE}")
logger.info(f"Vector database URL: {VECTOR_DB_URL}")

# --- 2. Memory System ---
# Long-term, and user-specific memory management
shago_memory = None
if FEATURES["enable_memory"]:
    try:
        shago_memory = Memory(db=memory_db)
        logger.info("Memory system enabled")
    except Exception as e:
        logger.warning(f"Could not initialize memory system: {e}")
        shago_memory = None

# --- 3. Knowledge Base (RAG) ---
# Load your database schema and other important context here
knowledge_base = None
if FEATURES["enable_rag"]:
    try:
        with open("knowledge_base/schema.sql", "r") as f:
            schema_info = f.read()

        knowledge_base = TextKnowledgeBase(
            texts=[schema_info],
            vector_db=PgVector(collection="shago_knowledge", connection_string=VECTOR_DB_URL)
        )
        # In a real application, you would load this once
        knowledge_base.load(recreate=True)
        logger.info("Knowledge base (RAG) enabled and loaded")
    except Exception as e:
        logger.warning(f"Could not load knowledge base: {e}")
        knowledge_base = None


# --- 4. Context Injection ---
# This context is dynamically injected into the system prompt
def get_dynamic_context():
    if not FEATURES["enable_context_injection"]:
        return {}

    # In a real app, you would fetch these from your database or other services
    total_inventory_value = "5,450,000 NGN"
    low_stock_items = "[{'id': 1, 'title': 'Gadget A', 'quantity': 5, 'category': 'Electronics'}]"
    last_inventory_update = "2024-07-14 10:00:00"

    context = {
        "shop_name": SHOP_NAME,
        "shop_address": SHOP_ADDRESS,
        "current_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "default_currency": DEFAULT_CURRENCY,
        "usd_to_naira_exchange_rate": USD_TO_NAIRA_EXCHANGE_RATE,
        "total_inventory_monetary_value": total_inventory_value,
        "top_10_lowest_stocked_items": low_stock_items,
        "last_inventory_item_updated": last_inventory_update,
    }

    # Add schema info if available
    if knowledge_base and os.path.exists("knowledge_base/schema.sql"):
        try:
            with open("knowledge_base/schema.sql", "r") as f:
                context["database_schema"] = f.read()
        except Exception as e:
            logger.warning(f"Could not read schema file: {e}")

    return context

# --- 5. The "Shago" Multi-Agent System ---
# This team orchestrates the different agents and tools.
logger.info("Initializing Shago multi-agent system...")

# Simple approach: qwen for conversation, SQL specialist for database queries
shago_team = Team(
    name="Shago",
    model=Ollama(id=MODELS["coordinator"]),  # qwen for general conversation
    members=[sql_agent],  # Only SQL agent for database queries
    storage=agent_storage if FEATURES["enable_session_persistence"] else None,
    memory=shago_memory,
    knowledge=knowledge_base,
    add_history_to_messages=True,
    show_tool_calls=True,
    instructions=[
        "You are Shago, a helpful AI assistant.",
        "For database questions about categories, inventory, or stock, delegate to the SQL_Agent.",
        "For general conversation, answer directly.",
        "Database keywords: categories, inventory, stock, items, products, database, what do we have"
    ]
)

logger.info("Shago team initialized successfully")

# --- 6. Backend and Frontend Setup ---
# Use Agno's Playground to create a simple web UI
logger.info("Setting up web interface...")

playground = Playground(
    teams=[shago_team],
    name=PLAYGROUND_CONFIG["name"],
    app_id=PLAYGROUND_CONFIG["app_id"],
    description=PLAYGROUND_CONFIG["description"]
)
app = playground.get_app()

# Add CORS middleware for local playground
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.info("Web interface ready")

if __name__ == "__main__":
    # This will start the FastAPI server
    logger.info(f"Starting Shago AI Assistant on {SERVER_HOST}:{SERVER_PORT}")
    playground.serve(
        app="main:app",
        host=SERVER_HOST,
        port=SERVER_PORT,
        reload=RELOAD_ON_CHANGE
    )
