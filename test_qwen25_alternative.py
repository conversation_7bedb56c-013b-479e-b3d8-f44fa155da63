#!/usr/bin/env python3
"""
Test qwen2.5:7b with different tool calling approaches
"""

import requests
import json
from config import OLLAMA_BASE_URL, MODELS

def test_qwen25_with_function_description():
    """Test qwen2.5 with function descriptions in system prompt"""
    
    system_prompt = """<no-think>
You are a helpful assistant. When users ask about weather, you should call the get_current_weather function.

Available functions:
- get_current_weather(location: str, unit: str = "celsius") -> str
  Gets current weather for a location

When you need to call a function, respond with:
FUNCTION_CALL: function_name(parameters)

Example: FUNCTION_CALL: get_current_weather(location="San Francisco", unit="celsius")
</no-think>"""
    
    user_message = "What's the weather like in San Francisco?"
    
    print("🧪 Testing qwen2.5:7b with function descriptions...")
    print(f"❓ User question: {user_message}")
    print("-" * 60)
    
    try:
        response = requests.post(
            f"{OLLAMA_BASE_URL}/api/chat",
            json={
                "model": MOD<PERSON><PERSON>["coordinator"],
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            message = result.get('message', {})
            content = message.get('content', '')
            
            print("✅ Response received!")
            print(f"💬 Content: {content}")
            
            # Check if it contains function call format
            if "FUNCTION_CALL:" in content:
                print("🔧 Function call detected in response!")
                return True
            else:
                print("❌ No function call format detected")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_qwen25_simple_routing():
    """Test qwen2.5 for simple routing decisions"""
    
    system_prompt = """<no-think>
You are a routing assistant. Analyze user requests and respond with one of these actions:
- ROUTE: sql_query (for database questions)
- ROUTE: weather_api (for weather questions)  
- ROUTE: general_chat (for general conversation)

Only respond with the ROUTE: action_name format.
</no-think>"""
    
    test_cases = [
        ("What's the weather in Tokyo?", "weather_api"),
        ("Show me all customers from the database", "sql_query"),
        ("Hello, how are you?", "general_chat")
    ]
    
    print("\n🧪 Testing qwen2.5:7b routing capabilities...")
    print("-" * 60)
    
    results = []
    
    for question, expected in test_cases:
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": MODELS["coordinator"],
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": question}
                    ],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('message', {}).get('content', '').strip()
                
                print(f"❓ Question: {question}")
                print(f"💬 Response: {content}")
                print(f"✅ Expected: ROUTE: {expected}")
                
                # Check if response contains expected route
                success = expected in content.lower()
                results.append(success)
                print(f"{'✅ PASS' if success else '❌ FAIL'}")
                print()
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    return all(results)

def test_qwen25_no_think_effectiveness():
    """Test if <no-think> flag actually works"""
    
    # Test with <no-think>
    system_with_no_think = """<no-think>
You are a helpful assistant. Respond briefly and directly.
</no-think>"""
    
    # Test without <no-think>
    system_without_no_think = """You are a helpful assistant. Respond briefly and directly."""
    
    question = "What is 2+2?"
    
    print("\n🧪 Testing <no-think> flag effectiveness...")
    print("-" * 60)
    
    for label, system_prompt in [("WITH <no-think>", system_with_no_think), ("WITHOUT <no-think>", system_without_no_think)]:
        try:
            response = requests.post(
                f"{OLLAMA_BASE_URL}/api/chat",
                json={
                    "model": MODELS["coordinator"],
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": question}
                    ],
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('message', {}).get('content', '')
                
                print(f"🧪 Test {label}:")
                print(f"💬 Response: {content}")
                print(f"📏 Length: {len(content)} characters")
                print()
                
        except Exception as e:
            print(f"❌ Error in {label}: {e}")

if __name__ == "__main__":
    print("🎯 Advanced Testing: qwen2.5:7b Capabilities")
    print("=" * 60)
    
    # Test 1: Function descriptions approach
    func_works = test_qwen25_with_function_description()
    
    # Test 2: Simple routing
    routing_works = test_qwen25_simple_routing()
    
    # Test 3: <no-think> effectiveness
    test_qwen25_no_think_effectiveness()
    
    print("=" * 60)
    print("🎯 FINAL RESULTS:")
    print(f"🔧 Function calling (description): {'PASS' if func_works else 'FAIL'}")
    print(f"🚦 Routing capabilities: {'PASS' if routing_works else 'FAIL'}")
    
    if func_works or routing_works:
        print("\n🎉 qwen2.5:7b shows promise for tool/function capabilities!")
        print("💡 May work better with custom formats than OpenAI tool calling")
    else:
        print("\n🤔 qwen2.5:7b may need different approaches for tool calling")
