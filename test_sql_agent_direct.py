#!/usr/bin/env python3
"""
Test the SQL agent directly to see if it works.
"""

from agents.sql_agent import sql_agent

def test_sql_agent_direct():
    """Test the SQL agent directly."""
    print("🔍 Testing SQL Agent directly...")
    
    try:
        response = sql_agent.run("What categories do we have in stock?")
        print(f"✅ SQL Agent Response: {response.content}")
        
        # Check if it contains actual data
        if any(word in response.content.lower() for word in ["electronics", "clothing", "books"]):
            print("🎉 SUCCESS: SQL Agent returned actual database data!")
        else:
            print("⚠️  SQL Agent responded but may not have executed SQL")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sql_agent_direct()
