#!/usr/bin/env python3
"""
Test qwen2.5:7b with tool calling support and <no-think> flag
Testing your theory about proper tool calling with qwen2.5 models
"""

import requests
import json
import time
from config import OLLAMA_BASE_URL, MODELS

def test_qwen25_tool_calling():
    """Test qwen2.5:7b with tool calling capabilities"""
    
    # Define a simple tool for testing
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_current_weather",
                "description": "Get the current weather in a given location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state, e.g. San Francisco, CA"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "The unit of temperature"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    # Test with <no-think> flag as you suggested
    system_prompt = """<no-think>
You are a helpful assistant with access to tools. When the user asks about weather, use the get_current_weather function.
Always respond with proper function calls when appropriate.
</no-think>"""
    
    user_message = "What's the weather like in San Francisco?"
    
    print("🧪 Testing qwen2.5:7b with tool calling...")
    print(f"📝 System prompt includes <no-think> flag")
    print(f"❓ User question: {user_message}")
    print("-" * 60)
    
    # Test the model
    try:
        response = requests.post(
            f"{OLLAMA_BASE_URL}/api/chat",
            json={
                "model": MODELS["coordinator"],  # qwen2.5:7b
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                "tools": tools,
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Response received!")
            print(f"📋 Model: {result.get('model', 'unknown')}")
            
            message = result.get('message', {})
            content = message.get('content', '')
            tool_calls = message.get('tool_calls', [])
            
            print(f"💬 Content: {content}")
            
            if tool_calls:
                print("🔧 Tool calls detected:")
                for i, tool_call in enumerate(tool_calls):
                    print(f"  {i+1}. Function: {tool_call.get('function', {}).get('name', 'unknown')}")
                    print(f"     Arguments: {tool_call.get('function', {}).get('arguments', {})}")
                return True
            else:
                print("❌ No tool calls detected")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_simple_conversation():
    """Test basic conversation without tools"""
    
    print("\n🧪 Testing qwen2.5:7b basic conversation...")
    
    try:
        response = requests.post(
            f"{OLLAMA_BASE_URL}/api/chat",
            json={
                "model": MODELS["coordinator"],  # qwen2.5:7b
                "messages": [
                    {"role": "user", "content": "Hello! Can you tell me what model you are?"}
                ],
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            message = result.get('message', {})
            content = message.get('content', '')
            
            print("✅ Basic conversation works!")
            print(f"💬 Response: {content[:200]}...")
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Testing Your Theory: qwen2.5 + <no-think> + Tool Calling")
    print("=" * 60)
    
    # Test 1: Basic conversation
    basic_works = test_simple_conversation()
    
    # Test 2: Tool calling with <no-think> flag
    if basic_works:
        time.sleep(1)  # Brief pause
        tool_works = test_qwen25_tool_calling()
        
        print("\n" + "=" * 60)
        print("🎯 RESULTS:")
        print(f"✅ Basic conversation: {'PASS' if basic_works else 'FAIL'}")
        print(f"🔧 Tool calling: {'PASS' if tool_works else 'FAIL'}")
        
        if tool_works:
            print("\n🎉 SUCCESS! Your theory was CORRECT!")
            print("✅ qwen2.5:7b + <no-think> flag = Working tool calls!")
        else:
            print("\n🤔 Tool calling didn't work as expected")
            print("💡 May need further investigation...")
    else:
        print("\n❌ Basic conversation failed - model may not be ready")
