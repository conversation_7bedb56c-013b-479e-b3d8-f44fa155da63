#!/usr/bin/env python3
"""
Setup script for Shago AI Assistant database.
This script initializes the SQLite database with sample data.
"""

import sqlite3
import os

def setup_database():
    """Initialize the database with schema and sample data."""
    db_file = "shago_offline.db"
    
    # Remove existing database if it exists
    if os.path.exists(db_file):
        print(f"Removing existing database: {db_file}")
        os.remove(db_file)
    
    # Create new database
    print(f"Creating new database: {db_file}")
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    try:
        # Read and execute schema
        with open("knowledge_base/schema.sql", "r") as f:
            schema_sql = f.read()
        
        print("Creating database schema...")
        cursor.executescript(schema_sql)
        
        # Insert sample data
        print("Inserting sample data...")
        
        # Sample categories
        categories = [
            (1, "Electronics", "Electronic devices and gadgets"),
            (2, "Clothing", "Apparel and fashion items"),
            (3, "Books", "Books and educational materials"),
            (4, "Home & Garden", "Home improvement and gardening supplies"),
            (5, "Sports", "Sports equipment and accessories")
        ]
        
        cursor.executemany(
            "INSERT INTO categories (id, title, description) VALUES (?, ?, ?)",
            categories
        )
        
        # Sample inventory items
        inventory_items = [
            (1, "Smartphone", "Latest model smartphone with advanced features", 15, 1),
            (2, "Laptop", "High-performance laptop for work and gaming", 8, 1),
            (3, "Wireless Headphones", "Noise-cancelling wireless headphones", 25, 1),
            (4, "T-Shirt", "Cotton t-shirt in various colors", 50, 2),
            (5, "Jeans", "Denim jeans in different sizes", 30, 2),
            (6, "Programming Book", "Learn Python programming", 20, 3),
            (7, "Novel", "Bestselling fiction novel", 35, 3),
            (8, "Garden Tools", "Set of basic gardening tools", 12, 4),
            (9, "Basketball", "Official size basketball", 18, 5),
            (10, "Running Shoes", "Comfortable running shoes", 22, 5)
        ]
        
        cursor.executemany(
            "INSERT INTO inventory (id, title, description, quantity, category_id) VALUES (?, ?, ?, ?, ?)",
            inventory_items
        )
        
        # Commit changes
        conn.commit()
        print("Database setup completed successfully!")
        
        # Display summary
        cursor.execute("SELECT COUNT(*) FROM categories")
        category_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM inventory")
        inventory_count = cursor.fetchone()[0]
        
        print(f"\nDatabase Summary:")
        print(f"- Categories: {category_count}")
        print(f"- Inventory Items: {inventory_count}")
        
    except Exception as e:
        print(f"Error setting up database: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    setup_database()
