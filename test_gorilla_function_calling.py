#!/usr/bin/env python3
"""
Test script to verify Gorilla OpenFunctions model for function calling.
This tests your theory about function calling models vs general conversation models.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.custom_tools import read_only_sql_executor
from config import MODELS

def test_gorilla_function_calling():
    """Test the Gorilla model specifically for function calling."""
    print("🦍 Testing Gorilla OpenFunctions Model for Function Calling")
    print("=" * 60)
    
    try:
        # Create an agent with Gorilla model and SQL tool
        gorilla_agent = Agent(
            name="Gorilla_Function_Caller",
            model=Ollama(id=MODELS["function_calling"]),  # Use Gorilla model
            tools=[read_only_sql_executor],
            instructions=[
                "You are a function calling specialist using the Gorilla OpenFunctions model.",
                "When asked about database content, CALL the read_only_sql_executor tool.",
                "For categories: call read_only_sql_executor('SELECT * FROM categories')",
                "For inventory: call read_only_sql_executor('SELECT * FROM inventory')",
                "DO NOT explain what you would do - actually call the function!"
            ]
        )
        
        print("✅ Gorilla agent created successfully")
        
        # Test 1: Database query that should trigger function calling
        print("\n1. Testing database query (should call function):")
        print("Query: 'What categories do we have in stock?'")
        
        try:
            response = gorilla_agent.run("What categories do we have in stock?")
            print(f"Response: {response.content}")
            
            # Check if the response contains actual data vs explanation
            if "SELECT" in response.content and "categories" in response.content.lower():
                if any(word in response.content.lower() for word in ["electronics", "clothing", "books"]):
                    print("🎉 SUCCESS: Gorilla model executed SQL and returned actual data!")
                else:
                    print("⚠️  PARTIAL: Gorilla model mentioned SQL but may not have executed it")
            else:
                print("❌ FAILED: Gorilla model explained instead of calling function")
                
        except Exception as e:
            print(f"❌ Error with Gorilla model: {e}")
        
        # Test 2: Compare with qwen model
        print("\n" + "=" * 60)
        print("2. Comparing with qwen model:")
        
        try:
            qwen_agent = Agent(
                name="Qwen_Agent",
                model=Ollama(id=MODELS["coordinator"]),  # qwen model
                tools=[read_only_sql_executor],
                instructions=[
                    "When asked about database content, CALL the read_only_sql_executor tool.",
                    "For categories: call read_only_sql_executor('SELECT * FROM categories')",
                    "DO NOT explain what you would do - actually call the function!"
                ]
            )
            
            response = qwen_agent.run("What categories do we have in stock?")
            print(f"Qwen Response: {response.content}")
            
            if any(word in response.content.lower() for word in ["electronics", "clothing", "books"]):
                print("✅ Qwen also executed SQL successfully")
            else:
                print("❌ Qwen explained instead of calling function")
                
        except Exception as e:
            print(f"❌ Error with qwen model: {e}")
            
    except Exception as e:
        print(f"❌ Failed to create Gorilla agent: {e}")
        print("Make sure the Gorilla model is fully downloaded:")
        print("ollama pull adrienbrault/gorilla-openfunctions-v2:Q3_K_M")

def check_gorilla_availability():
    """Check if Gorilla model is available."""
    import subprocess
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if 'adrienbrault/gorilla-openfunctions-v2' in result.stdout:
            print("✅ Gorilla model is available")
            return True
        else:
            print("❌ Gorilla model not found. Download with:")
            print("ollama pull adrienbrault/gorilla-openfunctions-v2:Q3_K_M")
            return False
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Checking Gorilla model availability...")
    if check_gorilla_availability():
        test_gorilla_function_calling()
    else:
        print("\n⏳ Please wait for the Gorilla model to finish downloading, then run this test again.")
