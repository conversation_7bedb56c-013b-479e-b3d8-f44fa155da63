#!/usr/bin/env python3
"""
Test script to verify Shago AI Assistant setup.
This script checks if all dependencies and models are available.
"""

import sys
import subprocess
import sqlite3
import os

def check_python_packages():
    """Check if required Python packages are installed."""
    print("Checking Python packages...")
    
    required_packages = [
        "agno",
        "fastapi", 
        "uvicorn",
        "sqlite3"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "sqlite3":
                import sqlite3
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    return missing_packages

def check_ollama_installation():
    """Check if Ollama is installed and running."""
    print("\nChecking Ollama installation...")
    
    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Ollama is installed and accessible")
            return True
        else:
            print("✗ Ollama command failed")
            return False
    except FileNotFoundError:
        print("✗ Ollama not found - please install from https://ollama.com/")
        return False

def check_ollama_models():
    """Check if required Ollama models are available."""
    print("\nChecking Ollama models...")
    
    required_models = ["qwen", "sqlcoder", "gorilla-openfunctions-v2"]
    missing_models = []
    
    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if result.returncode == 0:
            available_models = result.stdout.lower()
            
            for model in required_models:
                if model.lower() in available_models:
                    print(f"✓ {model}")
                else:
                    print(f"✗ {model} - MISSING")
                    missing_models.append(model)
        else:
            print("✗ Could not list Ollama models")
            return required_models
            
    except Exception as e:
        print(f"✗ Error checking models: {e}")
        return required_models
    
    return missing_models

def check_database():
    """Check if database file exists and is accessible."""
    print("\nChecking database...")
    
    db_file = "shago_offline.db"
    
    if not os.path.exists(db_file):
        print(f"✗ Database file {db_file} not found")
        print("  Run 'python setup_database.py' to create it")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ["categories", "inventory"]
        missing_tables = [table for table in expected_tables if table not in tables]
        
        if missing_tables:
            print(f"✗ Missing tables: {missing_tables}")
            return False
        else:
            print("✓ Database and tables exist")
            
            # Check sample data
            cursor.execute("SELECT COUNT(*) FROM categories")
            category_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM inventory")
            inventory_count = cursor.fetchone()[0]
            
            print(f"  - Categories: {category_count}")
            print(f"  - Inventory items: {inventory_count}")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def check_project_structure():
    """Check if all required files and directories exist."""
    print("\nChecking project structure...")
    
    required_files = [
        "main.py",
        "agents/__init__.py",
        "agents/sql_agent.py", 
        "agents/general_agent.py",
        "agents/tool_user_agent.py",
        "tools/__init__.py",
        "tools/custom_tools.py",
        "knowledge_base/schema.sql",
        "requirements.txt"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return missing_files

def main():
    """Run all checks and provide summary."""
    print("Shago AI Assistant - Setup Verification")
    print("=" * 50)
    
    # Run all checks
    missing_packages = check_python_packages()
    ollama_installed = check_ollama_installation()
    missing_models = check_ollama_models() if ollama_installed else []
    database_ok = check_database()
    missing_files = check_project_structure()
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    issues = []
    
    if missing_packages:
        issues.append(f"Missing Python packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r requirements.txt")
    
    if not ollama_installed:
        issues.append("Ollama not installed")
        print("Install Ollama from: https://ollama.com/")
    
    if missing_models:
        issues.append(f"Missing Ollama models: {', '.join(missing_models)}")
        for model in missing_models:
            print(f"Pull model with: ollama pull {model}")
    
    if not database_ok:
        issues.append("Database not set up")
        print("Set up database with: python setup_database.py")
    
    if missing_files:
        issues.append(f"Missing project files: {', '.join(missing_files)}")
    
    if not issues:
        print("✓ All checks passed! You can run the application with: python main.py")
        return 0
    else:
        print(f"\n✗ Found {len(issues)} issue(s) that need to be resolved:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
